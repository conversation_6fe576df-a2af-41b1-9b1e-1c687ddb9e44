@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\eslint@9.31.0_jiti@1.21.7\node_modules\eslint\bin\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\eslint@9.31.0_jiti@1.21.7\node_modules\eslint\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\eslint@9.31.0_jiti@1.21.7\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\eslint@9.31.0_jiti@1.21.7\node_modules\eslint\bin\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\eslint@9.31.0_jiti@1.21.7\node_modules\eslint\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\eslint@9.31.0_jiti@1.21.7\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\eslint@9.31.0_jiti@1.21.7\node_modules\eslint\bin\eslint.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\eslint@9.31.0_jiti@1.21.7\node_modules\eslint\bin\eslint.js" %*
)
