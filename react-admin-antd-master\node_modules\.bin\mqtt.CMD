@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\mqtt@5.13.3\node_modules\mqtt\build\bin\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\mqtt@5.13.3\node_modules\mqtt\build\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\mqtt@5.13.3\node_modules\mqtt\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\mqtt@5.13.3\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\mqtt@5.13.3\node_modules\mqtt\build\bin\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\mqtt@5.13.3\node_modules\mqtt\build\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\mqtt@5.13.3\node_modules\mqtt\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\mqtt@5.13.3\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\mqtt@5.13.3\node_modules\mqtt\build\bin\mqtt.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\mqtt@5.13.3\node_modules\mqtt\build\bin\mqtt.js" %*
)
