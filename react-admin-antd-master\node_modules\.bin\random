#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/mockjs@1.1.0/node_modules/mockjs/bin/node_modules:/mnt/c/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/mockjs@1.1.0/node_modules/mockjs/node_modules:/mnt/c/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/mockjs@1.1.0/node_modules:/mnt/c/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/mockjs@1.1.0/node_modules/mockjs/bin/node_modules:/mnt/c/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/mockjs@1.1.0/node_modules/mockjs/node_modules:/mnt/c/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/mockjs@1.1.0/node_modules:/mnt/c/Users/<USER>/Desktop/backup/react-admin-antd-master/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.pnpm/mockjs@1.1.0/node_modules/mockjs/bin/random" "$@"
else
  exec node  "$basedir/../.pnpm/mockjs@1.1.0/node_modules/mockjs/bin/random" "$@"
fi
