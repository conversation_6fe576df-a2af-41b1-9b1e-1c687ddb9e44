@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\webpack@5.100.2_esbuild@0.21.5\node_modules\webpack\bin\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\webpack@5.100.2_esbuild@0.21.5\node_modules\webpack\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\webpack@5.100.2_esbuild@0.21.5\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\webpack@5.100.2_esbuild@0.21.5\node_modules\webpack\bin\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\webpack@5.100.2_esbuild@0.21.5\node_modules\webpack\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\webpack@5.100.2_esbuild@0.21.5\node_modules;C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\webpack@5.100.2_esbuild@0.21.5\node_modules\webpack\bin\webpack.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\webpack@5.100.2_esbuild@0.21.5\node_modules\webpack\bin\webpack.js" %*
)
