hoistPattern:
  - '*'
hoistedDependencies:
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@ant-design/charts-util@0.0.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/charts-util': private
  '@ant-design/colors@7.2.1':
    '@ant-design/colors': private
  '@ant-design/cssinjs-utils@1.1.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/cssinjs-utils': private
  '@ant-design/cssinjs@1.24.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@ant-design/cssinjs': private
  '@ant-design/fast-color@2.0.6':
    '@ant-design/fast-color': private
  '@ant-design/icons-svg@4.4.2':
    '@ant-design/icons-svg': private
  '@ant-design/react-slick@1.1.2(react@18.3.1)':
    '@ant-design/react-slick': private
  '@antfu/install-pkg@1.1.0':
    '@antfu/install-pkg': private
  '@antfu/utils@8.1.1':
    '@antfu/utils': private
  '@antv/algorithm@0.1.26':
    '@antv/algorithm': private
  '@antv/component@2.1.5':
    '@antv/component': private
  '@antv/coord@0.4.7':
    '@antv/coord': private
  '@antv/event-emitter@0.1.3':
    '@antv/event-emitter': private
  '@antv/expr@1.0.2':
    '@antv/expr': private
  '@antv/g-camera-api@2.0.40':
    '@antv/g-camera-api': private
  '@antv/g-canvas@2.0.47':
    '@antv/g-canvas': private
  '@antv/g-dom-mutation-observer-api@2.0.37':
    '@antv/g-dom-mutation-observer-api': private
  '@antv/g-lite@2.3.1':
    '@antv/g-lite': private
  '@antv/g-math@3.0.1':
    '@antv/g-math': private
  '@antv/g-plugin-canvas-path-generator@2.1.21':
    '@antv/g-plugin-canvas-path-generator': private
  '@antv/g-plugin-canvas-picker@2.1.26':
    '@antv/g-plugin-canvas-picker': private
  '@antv/g-plugin-canvas-renderer@2.3.2':
    '@antv/g-plugin-canvas-renderer': private
  '@antv/g-plugin-dom-interaction@2.1.26':
    '@antv/g-plugin-dom-interaction': private
  '@antv/g-plugin-dragndrop@2.0.37':
    '@antv/g-plugin-dragndrop': private
  '@antv/g-plugin-html-renderer@2.1.26':
    '@antv/g-plugin-html-renderer': private
  '@antv/g-plugin-image-loader@2.1.25':
    '@antv/g-plugin-image-loader': private
  '@antv/g-plugin-svg-picker@2.0.41':
    '@antv/g-plugin-svg-picker': private
  '@antv/g-plugin-svg-renderer@2.2.23':
    '@antv/g-plugin-svg-renderer': private
  '@antv/g-web-animations-api@2.1.27':
    '@antv/g-web-animations-api': private
  '@antv/g2-extension-plot@0.2.2':
    '@antv/g2-extension-plot': private
  '@antv/g2@5.3.5':
    '@antv/g2': private
  '@antv/g@6.1.27':
    '@antv/g': private
  '@antv/graphlib@2.0.4':
    '@antv/graphlib': private
  '@antv/hierarchy@0.6.14':
    '@antv/hierarchy': private
  '@antv/layout@1.2.14-beta.9(workerize-loader@2.0.2(webpack@5.100.2(esbuild@0.21.5)))':
    '@antv/layout': private
  '@antv/scale@0.4.16':
    '@antv/scale': private
  '@antv/util@3.3.11':
    '@antv/util': private
  '@antv/vendor@1.0.11':
    '@antv/vendor': private
  '@apideck/better-ajv-errors@0.3.6(ajv@8.17.1)':
    '@apideck/better-ajv-errors': private
  '@babel/code-frame@7.27.1':
    '@babel/code-frame': private
  '@babel/compat-data@7.28.0':
    '@babel/compat-data': private
  '@babel/core@7.28.0':
    '@babel/core': private
  '@babel/generator@7.28.0':
    '@babel/generator': private
  '@babel/helper-annotate-as-pure@7.27.3':
    '@babel/helper-annotate-as-pure': private
  '@babel/helper-compilation-targets@7.27.2':
    '@babel/helper-compilation-targets': private
  '@babel/helper-create-class-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-class-features-plugin': private
  '@babel/helper-create-regexp-features-plugin@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-create-regexp-features-plugin': private
  '@babel/helper-define-polyfill-provider@0.6.5(@babel/core@7.28.0)':
    '@babel/helper-define-polyfill-provider': private
  '@babel/helper-globals@7.28.0':
    '@babel/helper-globals': private
  '@babel/helper-member-expression-to-functions@7.27.1':
    '@babel/helper-member-expression-to-functions': private
  '@babel/helper-module-imports@7.27.1':
    '@babel/helper-module-imports': private
  '@babel/helper-module-transforms@7.27.3(@babel/core@7.28.0)':
    '@babel/helper-module-transforms': private
  '@babel/helper-optimise-call-expression@7.27.1':
    '@babel/helper-optimise-call-expression': private
  '@babel/helper-plugin-utils@7.27.1':
    '@babel/helper-plugin-utils': private
  '@babel/helper-remap-async-to-generator@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-remap-async-to-generator': private
  '@babel/helper-replace-supers@7.27.1(@babel/core@7.28.0)':
    '@babel/helper-replace-supers': private
  '@babel/helper-skip-transparent-expression-wrappers@7.27.1':
    '@babel/helper-skip-transparent-expression-wrappers': private
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/helper-validator-option@7.27.1':
    '@babel/helper-validator-option': private
  '@babel/helper-wrap-function@7.27.1':
    '@babel/helper-wrap-function': private
  '@babel/helpers@7.27.6':
    '@babel/helpers': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-firefox-class-in-computed-class-key': private
  '@babel/plugin-bugfix-safari-class-field-initializer-scope@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-safari-class-field-initializer-scope': private
  '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': private
  '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': private
  '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': private
  '@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.28.0)':
    '@babel/plugin-proposal-private-property-in-object': private
  '@babel/plugin-syntax-import-assertions@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-assertions': private
  '@babel/plugin-syntax-import-attributes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-syntax-import-attributes': private
  '@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.28.0)':
    '@babel/plugin-syntax-unicode-sets-regex': private
  '@babel/plugin-transform-arrow-functions@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-arrow-functions': private
  '@babel/plugin-transform-async-generator-functions@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-async-generator-functions': private
  '@babel/plugin-transform-async-to-generator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-async-to-generator': private
  '@babel/plugin-transform-block-scoped-functions@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-block-scoped-functions': private
  '@babel/plugin-transform-block-scoping@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-block-scoping': private
  '@babel/plugin-transform-class-properties@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-class-properties': private
  '@babel/plugin-transform-class-static-block@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-class-static-block': private
  '@babel/plugin-transform-classes@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-classes': private
  '@babel/plugin-transform-computed-properties@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-computed-properties': private
  '@babel/plugin-transform-destructuring@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-destructuring': private
  '@babel/plugin-transform-dotall-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-dotall-regex': private
  '@babel/plugin-transform-duplicate-keys@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-duplicate-keys': private
  '@babel/plugin-transform-duplicate-named-capturing-groups-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-duplicate-named-capturing-groups-regex': private
  '@babel/plugin-transform-dynamic-import@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-dynamic-import': private
  '@babel/plugin-transform-explicit-resource-management@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-explicit-resource-management': private
  '@babel/plugin-transform-exponentiation-operator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-exponentiation-operator': private
  '@babel/plugin-transform-export-namespace-from@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-export-namespace-from': private
  '@babel/plugin-transform-for-of@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-for-of': private
  '@babel/plugin-transform-function-name@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-function-name': private
  '@babel/plugin-transform-json-strings@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-json-strings': private
  '@babel/plugin-transform-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-literals': private
  '@babel/plugin-transform-logical-assignment-operators@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-logical-assignment-operators': private
  '@babel/plugin-transform-member-expression-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-member-expression-literals': private
  '@babel/plugin-transform-modules-amd@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-amd': private
  '@babel/plugin-transform-modules-commonjs@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-commonjs': private
  '@babel/plugin-transform-modules-systemjs@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-systemjs': private
  '@babel/plugin-transform-modules-umd@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-modules-umd': private
  '@babel/plugin-transform-named-capturing-groups-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-named-capturing-groups-regex': private
  '@babel/plugin-transform-new-target@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-new-target': private
  '@babel/plugin-transform-nullish-coalescing-operator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-nullish-coalescing-operator': private
  '@babel/plugin-transform-numeric-separator@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-numeric-separator': private
  '@babel/plugin-transform-object-rest-spread@7.28.0(@babel/core@7.28.0)':
    '@babel/plugin-transform-object-rest-spread': private
  '@babel/plugin-transform-object-super@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-object-super': private
  '@babel/plugin-transform-optional-catch-binding@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-optional-catch-binding': private
  '@babel/plugin-transform-optional-chaining@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-optional-chaining': private
  '@babel/plugin-transform-parameters@7.27.7(@babel/core@7.28.0)':
    '@babel/plugin-transform-parameters': private
  '@babel/plugin-transform-private-methods@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-private-methods': private
  '@babel/plugin-transform-private-property-in-object@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-private-property-in-object': private
  '@babel/plugin-transform-property-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-property-literals': private
  '@babel/plugin-transform-react-jsx-self@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-self': private
  '@babel/plugin-transform-react-jsx-source@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-react-jsx-source': private
  '@babel/plugin-transform-regenerator@7.28.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-regenerator': private
  '@babel/plugin-transform-regexp-modifiers@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-regexp-modifiers': private
  '@babel/plugin-transform-reserved-words@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-reserved-words': private
  '@babel/plugin-transform-shorthand-properties@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-shorthand-properties': private
  '@babel/plugin-transform-spread@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-spread': private
  '@babel/plugin-transform-sticky-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-sticky-regex': private
  '@babel/plugin-transform-template-literals@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-template-literals': private
  '@babel/plugin-transform-typeof-symbol@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-typeof-symbol': private
  '@babel/plugin-transform-unicode-escapes@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-escapes': private
  '@babel/plugin-transform-unicode-property-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-property-regex': private
  '@babel/plugin-transform-unicode-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-regex': private
  '@babel/plugin-transform-unicode-sets-regex@7.27.1(@babel/core@7.28.0)':
    '@babel/plugin-transform-unicode-sets-regex': private
  '@babel/preset-env@7.28.0(@babel/core@7.28.0)':
    '@babel/preset-env': private
  '@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.28.0)':
    '@babel/preset-modules': private
  '@babel/runtime@7.27.6':
    '@babel/runtime': private
  '@babel/template@7.27.2':
    '@babel/template': private
  '@babel/traverse@7.28.0':
    '@babel/traverse': private
  '@babel/types@7.28.1':
    '@babel/types': private
  '@braintree/sanitize-url@7.1.1':
    '@braintree/sanitize-url': private
  '@bundled-es-modules/cookie@2.0.1':
    '@bundled-es-modules/cookie': private
  '@bundled-es-modules/statuses@1.0.1':
    '@bundled-es-modules/statuses': private
  '@bundled-es-modules/tough-cookie@0.1.6':
    '@bundled-es-modules/tough-cookie': private
  '@chevrotain/cst-dts-gen@11.0.3':
    '@chevrotain/cst-dts-gen': private
  '@chevrotain/gast@11.0.3':
    '@chevrotain/gast': private
  '@chevrotain/regexp-to-ast@11.0.3':
    '@chevrotain/regexp-to-ast': private
  '@chevrotain/types@11.0.3':
    '@chevrotain/types': private
  '@chevrotain/utils@11.0.3':
    '@chevrotain/utils': private
  '@dnd-kit/accessibility@3.1.1(react@18.3.1)':
    '@dnd-kit/accessibility': private
  '@emotion/babel-plugin@11.13.5':
    '@emotion/babel-plugin': private
  '@emotion/cache@11.14.0':
    '@emotion/cache': private
  '@emotion/css@11.13.5':
    '@emotion/css': private
  '@emotion/hash@0.8.0':
    '@emotion/hash': private
  '@emotion/memoize@0.9.0':
    '@emotion/memoize': private
  '@emotion/react@11.14.0(@types/react@18.3.23)(react@18.3.1)':
    '@emotion/react': private
  '@emotion/serialize@1.3.3':
    '@emotion/serialize': private
  '@emotion/sheet@1.4.0':
    '@emotion/sheet': private
  '@emotion/unitless@0.7.5':
    '@emotion/unitless': private
  '@emotion/use-insertion-effect-with-fallbacks@1.2.0(react@18.3.1)':
    '@emotion/use-insertion-effect-with-fallbacks': private
  '@emotion/utils@1.4.2':
    '@emotion/utils': private
  '@emotion/weak-memoize@0.4.0':
    '@emotion/weak-memoize': private
  '@esbuild/aix-ppc64@0.21.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.21.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.21.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.21.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.21.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.21.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.21.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.21.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.21.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.21.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.21.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.21.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.21.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.21.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.21.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.21.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.21.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.21.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.21.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.21.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.21.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@9.31.0(jiti@1.21.7))':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/config-array@0.21.0':
    '@eslint/config-array': private
  '@eslint/config-helpers@0.3.0':
    '@eslint/config-helpers': private
  '@eslint/core@0.15.1':
    '@eslint/core': private
  '@eslint/eslintrc@3.3.1':
    '@eslint/eslintrc': private
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': private
  '@eslint/plugin-kit@0.3.4':
    '@eslint/plugin-kit': private
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.3':
    '@humanwhocodes/retry': private
  '@iconify/types@2.0.0':
    '@iconify/types': private
  '@iconify/utils@2.3.0':
    '@iconify/utils': private
  '@inquirer/confirm@5.1.14(@types/node@22.16.5)':
    '@inquirer/confirm': private
  '@inquirer/core@10.1.15(@types/node@22.16.5)':
    '@inquirer/core': private
  '@inquirer/figures@1.0.13':
    '@inquirer/figures': private
  '@inquirer/type@3.0.8(@types/node@22.16.5)':
    '@inquirer/type': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.12':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/source-map@0.3.10':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.29':
    '@jridgewell/trace-mapping': private
  '@mermaid-js/parser@0.6.2':
    '@mermaid-js/parser': private
  '@monaco-editor/loader@1.5.0':
    '@monaco-editor/loader': private
  '@mswjs/interceptors@0.39.3':
    '@mswjs/interceptors': private
  '@naoak/workerize-transferable@0.1.0(workerize-loader@2.0.2(webpack@5.100.2(esbuild@0.21.5)))':
    '@naoak/workerize-transferable': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@open-draft/deferred-promise@2.2.0':
    '@open-draft/deferred-promise': private
  '@open-draft/logger@0.3.0':
    '@open-draft/logger': private
  '@open-draft/until@2.1.0':
    '@open-draft/until': private
  '@parcel/watcher-android-arm64@2.5.1':
    '@parcel/watcher-android-arm64': private
  '@parcel/watcher-darwin-arm64@2.5.1':
    '@parcel/watcher-darwin-arm64': private
  '@parcel/watcher-darwin-x64@2.5.1':
    '@parcel/watcher-darwin-x64': private
  '@parcel/watcher-freebsd-x64@2.5.1':
    '@parcel/watcher-freebsd-x64': private
  '@parcel/watcher-linux-arm-glibc@2.5.1':
    '@parcel/watcher-linux-arm-glibc': private
  '@parcel/watcher-linux-arm-musl@2.5.1':
    '@parcel/watcher-linux-arm-musl': private
  '@parcel/watcher-linux-arm64-glibc@2.5.1':
    '@parcel/watcher-linux-arm64-glibc': private
  '@parcel/watcher-linux-arm64-musl@2.5.1':
    '@parcel/watcher-linux-arm64-musl': private
  '@parcel/watcher-linux-x64-glibc@2.5.1':
    '@parcel/watcher-linux-x64-glibc': private
  '@parcel/watcher-linux-x64-musl@2.5.1':
    '@parcel/watcher-linux-x64-musl': private
  '@parcel/watcher-win32-arm64@2.5.1':
    '@parcel/watcher-win32-arm64': private
  '@parcel/watcher-win32-ia32@2.5.1':
    '@parcel/watcher-win32-ia32': private
  '@parcel/watcher-win32-x64@2.5.1':
    '@parcel/watcher-win32-x64': private
  '@parcel/watcher@2.5.1':
    '@parcel/watcher': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@rc-component/async-validator@5.0.4':
    '@rc-component/async-validator': private
  '@rc-component/color-picker@2.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/color-picker': private
  '@rc-component/context@1.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/context': private
  '@rc-component/mini-decimal@1.1.0':
    '@rc-component/mini-decimal': private
  '@rc-component/mutate-observer@1.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/mutate-observer': private
  '@rc-component/portal@1.1.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/portal': private
  '@rc-component/qrcode@1.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/qrcode': private
  '@rc-component/tour@1.15.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/tour': private
  '@rc-component/trigger@2.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@rc-component/trigger': private
  '@reactflow/background@11.3.14(@types/react@18.3.23)(immer@9.0.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@reactflow/background': private
  '@reactflow/controls@11.2.14(@types/react@18.3.23)(immer@9.0.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@reactflow/controls': private
  '@reactflow/minimap@11.7.14(@types/react@18.3.23)(immer@9.0.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@reactflow/minimap': private
  '@reactflow/node-resizer@2.2.14(@types/react@18.3.23)(immer@9.0.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@reactflow/node-resizer': private
  '@reactflow/node-toolbar@1.3.14(@types/react@18.3.23)(immer@9.0.21)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)':
    '@reactflow/node-toolbar': private
  '@remix-run/router@1.23.0':
    '@remix-run/router': private
  '@rolldown/pluginutils@1.0.0-beta.27':
    '@rolldown/pluginutils': private
  '@rollup/plugin-babel@5.3.1(@babel/core@7.28.0)(@types/babel__core@7.20.5)(rollup@2.79.2)':
    '@rollup/plugin-babel': private
  '@rollup/plugin-node-resolve@15.3.1(rollup@2.79.2)':
    '@rollup/plugin-node-resolve': private
  '@rollup/plugin-replace@2.4.2(rollup@2.79.2)':
    '@rollup/plugin-replace': private
  '@rollup/plugin-terser@0.4.4(rollup@2.79.2)':
    '@rollup/plugin-terser': private
  '@rollup/pluginutils@4.2.1':
    '@rollup/pluginutils': private
  '@rollup/rollup-android-arm-eabi@4.45.1':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.45.1':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.45.1':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.45.1':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.45.1':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.45.1':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.45.1':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.45.1':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.45.1':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.45.1':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.45.1':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.45.1':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.45.1':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-riscv64-musl@4.45.1':
    '@rollup/rollup-linux-riscv64-musl': private
  '@rollup/rollup-linux-s390x-gnu@4.45.1':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.45.1':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.45.1':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.45.1':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.45.1':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.45.1':
    '@rollup/rollup-win32-x64-msvc': private
  '@surma/rollup-plugin-off-main-thread@2.2.3':
    '@surma/rollup-plugin-off-main-thread': private
  '@swc/core-darwin-arm64@1.13.1':
    '@swc/core-darwin-arm64': private
  '@swc/core-darwin-x64@1.13.1':
    '@swc/core-darwin-x64': private
  '@swc/core-linux-arm-gnueabihf@1.13.1':
    '@swc/core-linux-arm-gnueabihf': private
  '@swc/core-linux-arm64-gnu@1.13.1':
    '@swc/core-linux-arm64-gnu': private
  '@swc/core-linux-arm64-musl@1.13.1':
    '@swc/core-linux-arm64-musl': private
  '@swc/core-linux-x64-gnu@1.13.1':
    '@swc/core-linux-x64-gnu': private
  '@swc/core-linux-x64-musl@1.13.1':
    '@swc/core-linux-x64-musl': private
  '@swc/core-win32-arm64-msvc@1.13.1':
    '@swc/core-win32-arm64-msvc': private
  '@swc/core-win32-ia32-msvc@1.13.1':
    '@swc/core-win32-ia32-msvc': private
  '@swc/core-win32-x64-msvc@1.13.1':
    '@swc/core-win32-x64-msvc': private
  '@swc/core@1.13.1':
    '@swc/core': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/types@0.1.23':
    '@swc/types': private
  '@transloadit/prettier-bytes@0.0.7':
    '@transloadit/prettier-bytes': private
  '@tsparticles/basic@3.8.1':
    '@tsparticles/basic': private
  '@tsparticles/engine@3.8.1':
    '@tsparticles/engine': private
  '@tsparticles/interaction-external-attract@3.8.1':
    '@tsparticles/interaction-external-attract': private
  '@tsparticles/interaction-external-bounce@3.8.1':
    '@tsparticles/interaction-external-bounce': private
  '@tsparticles/interaction-external-bubble@3.8.1':
    '@tsparticles/interaction-external-bubble': private
  '@tsparticles/interaction-external-connect@3.8.1':
    '@tsparticles/interaction-external-connect': private
  '@tsparticles/interaction-external-grab@3.8.1':
    '@tsparticles/interaction-external-grab': private
  '@tsparticles/interaction-external-pause@3.8.1':
    '@tsparticles/interaction-external-pause': private
  '@tsparticles/interaction-external-push@3.8.1':
    '@tsparticles/interaction-external-push': private
  '@tsparticles/interaction-external-remove@3.8.1':
    '@tsparticles/interaction-external-remove': private
  '@tsparticles/interaction-external-repulse@3.8.1':
    '@tsparticles/interaction-external-repulse': private
  '@tsparticles/interaction-external-slow@3.8.1':
    '@tsparticles/interaction-external-slow': private
  '@tsparticles/interaction-external-trail@3.8.1':
    '@tsparticles/interaction-external-trail': private
  '@tsparticles/interaction-particles-attract@3.8.1':
    '@tsparticles/interaction-particles-attract': private
  '@tsparticles/interaction-particles-collisions@3.8.1':
    '@tsparticles/interaction-particles-collisions': private
  '@tsparticles/interaction-particles-links@3.8.1':
    '@tsparticles/interaction-particles-links': private
  '@tsparticles/move-base@3.8.1':
    '@tsparticles/move-base': private
  '@tsparticles/move-parallax@3.8.1':
    '@tsparticles/move-parallax': private
  '@tsparticles/plugin-absorbers@3.8.1':
    '@tsparticles/plugin-absorbers': private
  '@tsparticles/plugin-easing-quad@3.8.1':
    '@tsparticles/plugin-easing-quad': private
  '@tsparticles/plugin-emitters-shape-circle@3.8.1':
    '@tsparticles/plugin-emitters-shape-circle': private
  '@tsparticles/plugin-emitters-shape-square@3.8.1':
    '@tsparticles/plugin-emitters-shape-square': private
  '@tsparticles/plugin-emitters@3.8.1':
    '@tsparticles/plugin-emitters': private
  '@tsparticles/plugin-hex-color@3.8.1':
    '@tsparticles/plugin-hex-color': private
  '@tsparticles/plugin-hsl-color@3.8.1':
    '@tsparticles/plugin-hsl-color': private
  '@tsparticles/plugin-rgb-color@3.8.1':
    '@tsparticles/plugin-rgb-color': private
  '@tsparticles/shape-circle@3.8.1':
    '@tsparticles/shape-circle': private
  '@tsparticles/shape-emoji@3.8.1':
    '@tsparticles/shape-emoji': private
  '@tsparticles/shape-image@3.8.1':
    '@tsparticles/shape-image': private
  '@tsparticles/shape-line@3.8.1':
    '@tsparticles/shape-line': private
  '@tsparticles/shape-polygon@3.8.1':
    '@tsparticles/shape-polygon': private
  '@tsparticles/shape-square@3.8.1':
    '@tsparticles/shape-square': private
  '@tsparticles/shape-star@3.8.1':
    '@tsparticles/shape-star': private
  '@tsparticles/shape-text@3.8.1':
    '@tsparticles/shape-text': private
  '@tsparticles/slim@3.8.1':
    '@tsparticles/slim': private
  '@tsparticles/updater-color@3.8.1':
    '@tsparticles/updater-color': private
  '@tsparticles/updater-destroy@3.8.1':
    '@tsparticles/updater-destroy': private
  '@tsparticles/updater-life@3.8.1':
    '@tsparticles/updater-life': private
  '@tsparticles/updater-opacity@3.8.1':
    '@tsparticles/updater-opacity': private
  '@tsparticles/updater-out-modes@3.8.1':
    '@tsparticles/updater-out-modes': private
  '@tsparticles/updater-roll@3.8.1':
    '@tsparticles/updater-roll': private
  '@tsparticles/updater-rotate@3.8.1':
    '@tsparticles/updater-rotate': private
  '@tsparticles/updater-size@3.8.1':
    '@tsparticles/updater-size': private
  '@tsparticles/updater-stroke-color@3.8.1':
    '@tsparticles/updater-stroke-color': private
  '@tsparticles/updater-tilt@3.8.1':
    '@tsparticles/updater-tilt': private
  '@tsparticles/updater-twinkle@3.8.1':
    '@tsparticles/updater-twinkle': private
  '@tsparticles/updater-wobble@3.8.1':
    '@tsparticles/updater-wobble': private
  '@types/babel__core@7.20.5':
    '@types/babel__core': private
  '@types/babel__generator@7.27.0':
    '@types/babel__generator': private
  '@types/babel__template@7.4.4':
    '@types/babel__template': private
  '@types/babel__traverse@7.20.7':
    '@types/babel__traverse': private
  '@types/cookie@0.6.0':
    '@types/cookie': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-axis@3.0.6':
    '@types/d3-axis': private
  '@types/d3-brush@3.0.6':
    '@types/d3-brush': private
  '@types/d3-chord@3.0.6':
    '@types/d3-chord': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-contour@3.0.6':
    '@types/d3-contour': private
  '@types/d3-delaunay@6.0.4':
    '@types/d3-delaunay': private
  '@types/d3-dispatch@3.0.6':
    '@types/d3-dispatch': private
  '@types/d3-drag@3.0.7':
    '@types/d3-drag': private
  '@types/d3-dsv@3.0.7':
    '@types/d3-dsv': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-fetch@3.0.7':
    '@types/d3-fetch': private
  '@types/d3-force@3.0.10':
    '@types/d3-force': private
  '@types/d3-format@3.0.4':
    '@types/d3-format': private
  '@types/d3-geo@3.1.0':
    '@types/d3-geo': private
  '@types/d3-hierarchy@3.1.7':
    '@types/d3-hierarchy': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-polygon@3.0.2':
    '@types/d3-polygon': private
  '@types/d3-quadtree@3.0.6':
    '@types/d3-quadtree': private
  '@types/d3-random@3.0.3':
    '@types/d3-random': private
  '@types/d3-scale-chromatic@3.1.0':
    '@types/d3-scale-chromatic': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-selection@3.0.11':
    '@types/d3-selection': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time-format@4.0.3':
    '@types/d3-time-format': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/d3-transition@3.0.9':
    '@types/d3-transition': private
  '@types/d3-zoom@3.0.8':
    '@types/d3-zoom': private
  '@types/d3@7.4.3':
    '@types/d3': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/eslint@9.6.1':
    '@types/eslint': private
  '@types/estree-jsx@1.0.5':
    '@types/estree-jsx': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/event-emitter@0.3.5':
    '@types/event-emitter': private
  '@types/geojson@7946.0.16':
    '@types/geojson': private
  '@types/hast@3.0.4':
    '@types/hast': private
  '@types/hoist-non-react-statics@3.3.7(@types/react@18.3.23)':
    '@types/hoist-non-react-statics': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/katex@0.16.7':
    '@types/katex': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/parse-json@4.0.2':
    '@types/parse-json': private
  '@types/prop-types@15.7.15':
    '@types/prop-types': private
  '@types/react-redux@7.1.34':
    '@types/react-redux': private
  '@types/readable-stream@4.0.21':
    '@types/readable-stream': private
  '@types/resolve@1.20.2':
    '@types/resolve': private
  '@types/statuses@2.0.6':
    '@types/statuses': private
  '@types/tough-cookie@4.0.5':
    '@types/tough-cookie': private
  '@types/trusted-types@2.0.7':
    '@types/trusted-types': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@types/ws@8.18.1':
    '@types/ws': private
  '@typescript-eslint/eslint-plugin@8.38.0(@typescript-eslint/parser@8.38.0(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3))(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3)':
    '@typescript-eslint/eslint-plugin': private
  '@typescript-eslint/parser@8.38.0(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3)':
    '@typescript-eslint/parser': private
  '@typescript-eslint/project-service@8.38.0(typescript@5.8.3)':
    '@typescript-eslint/project-service': private
  '@typescript-eslint/scope-manager@8.38.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/tsconfig-utils@8.38.0(typescript@5.8.3)':
    '@typescript-eslint/tsconfig-utils': private
  '@typescript-eslint/type-utils@8.38.0(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@8.38.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@8.38.0(typescript@5.8.3)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@8.38.0(eslint@9.31.0(jiti@1.21.7))(typescript@5.8.3)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@8.38.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@uppy/companion-client@2.2.2':
    '@uppy/companion-client': private
  '@uppy/core@2.3.4':
    '@uppy/core': private
  '@uppy/store-default@2.1.1':
    '@uppy/store-default': private
  '@uppy/utils@4.1.3':
    '@uppy/utils': private
  '@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4)':
    '@uppy/xhr-upload': private
  '@wangeditor/basic-modules@1.1.7(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.1.5)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@5.1.5)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/basic-modules': private
  '@wangeditor/code-highlight@1.0.3(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.11)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/code-highlight': private
  '@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@5.1.5)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/core': private
  '@wangeditor/list-module@1.0.5(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.11)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/list-module': private
  '@wangeditor/table-module@1.1.4(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.11)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(nanoid@3.3.11)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/table-module': private
  '@wangeditor/upload-image-module@1.0.2(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor/basic-modules@1.1.7(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.11)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.throttle@4.1.1)(nanoid@3.3.11)(slate@0.72.8)(snabbdom@3.6.2))(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.11)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(lodash.foreach@4.5.0)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/upload-image-module': private
  '@wangeditor/video-module@1.1.4(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(@wangeditor/core@1.1.19(@uppy/core@2.3.4)(@uppy/xhr-upload@2.1.3(@uppy/core@2.3.4))(dom7@3.0.0)(is-hotkey@0.2.0)(lodash.camelcase@4.3.0)(lodash.clonedeep@4.5.0)(lodash.debounce@4.0.8)(lodash.foreach@4.5.0)(lodash.isequal@4.5.0)(lodash.throttle@4.1.1)(lodash.toarray@4.4.0)(nanoid@3.3.11)(slate@0.72.8)(snabbdom@3.6.2))(dom7@3.0.0)(nanoid@3.3.11)(slate@0.72.8)(snabbdom@3.6.2)':
    '@wangeditor/video-module': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  abort-controller@3.0.0:
    abort-controller: private
  acorn-import-phases@1.0.4(acorn@8.15.0):
    acorn-import-phases: private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-escapes@4.3.2:
    ansi-escapes: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  async-function@1.0.0:
    async-function: private
  async@3.2.6:
    async: private
  asynckit@0.4.0:
    asynckit: private
  at-least-node@1.0.0:
    at-least-node: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  babel-plugin-macros@3.1.0:
    babel-plugin-macros: private
  babel-plugin-polyfill-corejs2@0.4.14(@babel/core@7.28.0):
    babel-plugin-polyfill-corejs2: private
  babel-plugin-polyfill-corejs3@0.13.0(@babel/core@7.28.0):
    babel-plugin-polyfill-corejs3: private
  babel-plugin-polyfill-regenerator@0.6.5(@babel/core@7.28.0):
    babel-plugin-polyfill-regenerator: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  big.js@5.2.2:
    big.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bl@6.1.0:
    bl: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.25.1:
    browserslist: private
  bubblesets-js@2.3.4:
    bubblesets-js: private
  buffer-from@1.1.2:
    buffer-from: private
  buffer@6.0.3:
    buffer: private
  bundle-require@4.2.1(esbuild@0.21.5):
    bundle-require: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camel-case@4.1.2:
    camel-case: private
  camelcase-css@2.0.1:
    camelcase-css: private
  caniuse-lite@1.0.30001727:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities-html4@2.1.0:
    character-entities-html4: private
  character-entities-legacy@1.1.4:
    character-entities-legacy: private
  character-entities@1.2.4:
    character-entities: private
  character-reference-invalid@1.1.4:
    character-reference-invalid: private
  chevrotain-allstar@0.3.1(chevrotain@11.0.3):
    chevrotain-allstar: private
  chevrotain@11.0.3:
    chevrotain: private
  chokidar@4.0.3:
    chokidar: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  classcat@5.0.5:
    classcat: private
  clean-css@5.3.3:
    clean-css: private
  cli-width@4.1.0:
    cli-width: private
  cliui@8.0.1:
    cliui: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  colorette@2.0.20:
    colorette: private
  combined-stream@1.0.8:
    combined-stream: private
  comlink@4.4.2:
    comlink: private
  comma-separated-tokens@2.0.3:
    comma-separated-tokens: private
  commander@8.3.0:
    commander: private
  commist@3.2.0:
    commist: private
  common-tags@1.8.2:
    common-tags: private
  compute-scroll-into-view@3.1.1:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  concat-stream@2.0.0:
    concat-stream: private
  confbox@0.1.8:
    confbox: private
  connect-history-api-fallback@1.6.0:
    connect-history-api-fallback: private
  connect@3.7.0:
    connect: private
  consola@2.15.3:
    consola: private
  convert-source-map@2.0.0:
    convert-source-map: private
  cookie@0.7.2:
    cookie: private
  copy-to-clipboard@3.3.3:
    copy-to-clipboard: private
  core-js-compat@3.44.0:
    core-js-compat: private
  cose-base@1.0.3:
    cose-base: private
  cosmiconfig@7.1.0:
    cosmiconfig: private
  cross-spawn@7.0.6:
    cross-spawn: private
  crypto-random-string@2.0.0:
    crypto-random-string: private
  css-box-model@1.2.1:
    css-box-model: private
  css-select@4.3.0:
    css-select: private
  css-what@6.2.2:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  cytoscape-cose-bilkent@4.1.0(cytoscape@3.33.0):
    cytoscape-cose-bilkent: private
  cytoscape-fcose@2.2.0(cytoscape@3.33.0):
    cytoscape-fcose: private
  cytoscape@3.33.0:
    cytoscape: private
  d3-array@2.12.1:
    d3-array: private
  d3-axis@3.0.0:
    d3-axis: private
  d3-binarytree@1.0.2:
    d3-binarytree: private
  d3-brush@3.0.0:
    d3-brush: private
  d3-chord@3.0.1:
    d3-chord: private
  d3-color@3.1.0:
    d3-color: private
  d3-contour@4.0.2:
    d3-contour: private
  d3-delaunay@6.0.4:
    d3-delaunay: private
  d3-dispatch@3.0.1:
    d3-dispatch: private
  d3-drag@3.0.0:
    d3-drag: private
  d3-dsv@3.0.1:
    d3-dsv: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-fetch@3.0.1:
    d3-fetch: private
  d3-force-3d@3.0.6:
    d3-force-3d: private
  d3-force@3.0.0:
    d3-force: private
  d3-format@3.1.0:
    d3-format: private
  d3-geo-projection@4.0.0:
    d3-geo-projection: private
  d3-geo@3.1.1:
    d3-geo: private
  d3-hierarchy@3.1.2:
    d3-hierarchy: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-octree@1.1.0:
    d3-octree: private
  d3-path@3.1.0:
    d3-path: private
  d3-polygon@3.0.1:
    d3-polygon: private
  d3-quadtree@3.0.1:
    d3-quadtree: private
  d3-random@3.0.1:
    d3-random: private
  d3-regression@1.3.10:
    d3-regression: private
  d3-sankey@0.12.3:
    d3-sankey: private
  d3-scale-chromatic@3.1.0:
    d3-scale-chromatic: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-selection@3.0.0:
    d3-selection: private
  d3-shape@1.3.7:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  d3-transition@3.0.1(d3-selection@3.0.0):
    d3-transition: private
  d3-zoom@3.0.0:
    d3-zoom: private
  d3@7.9.0:
    d3: private
  d@1.0.2:
    d: private
  dagre-d3-es@7.0.11:
    dagre-d3-es: private
  dagre@0.8.5:
    dagre: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.1:
    debug: private
  decode-named-character-reference@1.2.0:
    decode-named-character-reference: private
  deep-is@0.1.4:
    deep-is: private
  deepmerge@4.3.1:
    deepmerge: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@2.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  delaunator@5.0.1:
    delaunator: private
  delayed-stream@1.0.0:
    delayed-stream: private
  dequal@2.0.3:
    dequal: private
  detect-libc@1.0.3:
    detect-libc: private
  devlop@1.1.0:
    devlop: private
  didyoumean@1.2.2:
    didyoumean: private
  dlv@1.1.3:
    dlv: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dom-serializer@1.4.1:
    dom-serializer: private
  dom7@3.0.0:
    dom7: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@4.3.1:
    domhandler: private
  dompurify@3.2.6:
    dompurify: private
  domutils@2.8.0:
    domutils: private
  dot-case@3.0.4:
    dot-case: private
  dotenv-expand@8.0.3:
    dotenv-expand: private
  dotenv@16.6.1:
    dotenv: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  ejs@3.1.10:
    ejs: private
  electron-to-chromium@1.5.189:
    electron-to-chromium: private
  emoji-regex@8.0.0:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  encodeurl@1.0.2:
    encodeurl: private
  enhanced-resolve@5.18.2:
    enhanced-resolve: private
  entities@6.0.1:
    entities: private
  error-ex@1.3.2:
    error-ex: private
  es-abstract@1.24.0:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-module-lexer@1.7.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  es5-ext@0.10.64:
    es5-ext: private
  es6-iterator@2.0.3:
    es6-iterator: private
  es6-symbol@3.1.4:
    es6-symbol: private
  esbuild@0.21.5:
    esbuild: private
  escalade@3.2.0:
    escalade: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.4.0:
    eslint-scope: private
  eslint-visitor-keys@4.2.1:
    eslint-visitor-keys: private
  esniff@2.0.1:
    esniff: private
  espree@10.4.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-util-is-identifier-name@3.0.0:
    estree-util-is-identifier-name: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  event-emitter@0.3.5:
    event-emitter: private
  event-target-shim@5.0.1:
    event-target-shim: private
  eventemitter3@5.0.1:
    eventemitter3: private
  events@3.3.0:
    events: private
  exsolve@1.0.7:
    exsolve: private
  ext@1.7.0:
    ext: private
  extend@3.0.2:
    extend: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-unique-numbers@8.0.13:
    fast-unique-numbers: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fault@1.0.4:
    fault: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  fecha@4.2.3:
    fecha: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.1.2:
    finalhandler: private
  find-root@1.1.0:
    find-root: private
  find-up@5.0.0:
    find-up: private
  flairup@1.0.0:
    flairup: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  flru@1.0.2:
    flru: private
  follow-redirects@1.15.9:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data@4.0.4:
    form-data: private
  format@0.2.2:
    format: private
  fraction.js@4.3.7:
    fraction.js: private
  fs-extra@10.1.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  gensync@1.0.0-beta.2:
    gensync: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-own-enumerable-property-symbols@3.0.2:
    get-own-enumerable-property-symbols: private
  get-proto@1.0.1:
    get-proto: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  gl-matrix@3.4.3:
    gl-matrix: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@10.4.5:
    glob: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  graphlib@2.1.8:
    graphlib: private
  graphql@16.11.0:
    graphql: private
  hachure-fill@0.5.2:
    hachure-fill: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hast-util-from-dom@5.0.1:
    hast-util-from-dom: private
  hast-util-from-html-isomorphic@2.0.0:
    hast-util-from-html-isomorphic: private
  hast-util-from-html@2.0.3:
    hast-util-from-html: private
  hast-util-from-parse5@8.0.3:
    hast-util-from-parse5: private
  hast-util-is-element@3.0.0:
    hast-util-is-element: private
  hast-util-parse-selector@2.2.5:
    hast-util-parse-selector: private
  hast-util-to-jsx-runtime@2.3.6:
    hast-util-to-jsx-runtime: private
  hast-util-to-text@4.0.2:
    hast-util-to-text: private
  hast-util-whitespace@3.0.0:
    hast-util-whitespace: private
  hastscript@6.0.0:
    hastscript: private
  he@1.2.0:
    he: private
  headers-polyfill@4.0.3:
    headers-polyfill: private
  help-me@5.0.0:
    help-me: private
  highlightjs-vue@1.0.0:
    highlightjs-vue: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  html-minifier-terser@6.1.0:
    html-minifier-terser: private
  html-parse-stringify@3.0.1:
    html-parse-stringify: private
  html-url-attributes@3.0.1:
    html-url-attributes: private
  html-void-elements@2.0.1:
    html-void-elements: private
  iconv-lite@0.6.3:
    iconv-lite: private
  idb@7.1.1:
    idb: private
  ieee754@1.2.1:
    ieee754: private
  ignore@5.3.2:
    ignore: private
  immer@9.0.21:
    immer: private
  immutable@5.1.3:
    immutable: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  inline-style-parser@0.2.4:
    inline-style-parser: private
  internal-slot@1.1.0:
    internal-slot: private
  internmap@1.0.1:
    internmap: private
  intersection-observer@0.12.2:
    intersection-observer: private
  ip-address@9.0.5:
    ip-address: private
  is-alphabetical@1.0.4:
    is-alphabetical: private
  is-alphanumerical@1.0.4:
    is-alphanumerical: private
  is-any-array@2.0.1:
    is-any-array: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-decimal@1.0.4:
    is-decimal: private
  is-docker@2.2.1:
    is-docker: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hexadecimal@1.0.4:
    is-hexadecimal: private
  is-hotkey@0.2.0:
    is-hotkey: private
  is-map@2.0.3:
    is-map: private
  is-module@1.0.0:
    is-module: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-node-process@1.2.0:
    is-node-process: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-obj@1.0.1:
    is-obj: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-reference@1.2.1:
    is-reference: private
  is-regex@1.2.1:
    is-regex: private
  is-regexp@1.0.0:
    is-regexp: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-url@1.2.4:
    is-url: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  is-wsl@2.2.0:
    is-wsl: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  jackspeak@3.4.3:
    jackspeak: private
  jake@10.9.2:
    jake: private
  jest-worker@27.5.1:
    jest-worker: private
  jiti@1.21.7:
    jiti: private
  js-cookie@3.0.5:
    js-cookie: private
  js-sdsl@4.3.0:
    js-sdsl: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  jsbn@1.1.0:
    jsbn: private
  jsesc@3.1.0:
    jsesc: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json2mq@0.2.0:
    json2mq: private
  json5@2.2.3:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  jsonpointer@5.0.1:
    jsonpointer: private
  keyv@4.5.4:
    keyv: private
  khroma@2.1.0:
    khroma: private
  kolorist@1.8.0:
    kolorist: private
  langium@3.3.1:
    langium: private
  layout-base@1.0.2:
    layout-base: private
  leven@3.1.0:
    leven: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  load-tsconfig@0.2.5:
    load-tsconfig: private
  loader-runner@4.3.0:
    loader-runner: private
  loader-utils@2.0.4:
    loader-utils: private
  local-pkg@1.1.1:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash.camelcase@4.3.0:
    lodash.camelcase: private
  lodash.castarray@4.4.0:
    lodash.castarray: private
  lodash.clonedeep@4.5.0:
    lodash.clonedeep: private
  lodash.debounce@4.0.8:
    lodash.debounce: private
  lodash.escape@4.0.1:
    lodash.escape: private
  lodash.foreach@4.5.0:
    lodash.foreach: private
  lodash.isequal@4.5.0:
    lodash.isequal: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash.sortby@4.7.0:
    lodash.sortby: private
  lodash.throttle@4.1.1:
    lodash.throttle: private
  lodash.toarray@4.4.0:
    lodash.toarray: private
  lodash.unescape@4.0.1:
    lodash.unescape: private
  lodash@4.17.21:
    lodash: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lower-case@2.0.2:
    lower-case: private
  lowlight@1.20.0:
    lowlight: private
  lru-cache@10.4.3:
    lru-cache: private
  magic-string@0.25.9:
    magic-string: private
  markdown-table@3.0.4:
    markdown-table: private
  marked@4.3.0:
    marked: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-math@3.0.0:
    mdast-util-math: private
  mdast-util-mdx-expression@2.0.1:
    mdast-util-mdx-expression: private
  mdast-util-mdx-jsx@3.2.0:
    mdast-util-mdx-jsx: private
  mdast-util-mdxjs-esm@2.0.1:
    mdast-util-mdxjs-esm: private
  mdast-util-newline-to-break@2.0.0:
    mdast-util-newline-to-break: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-hast@13.2.0:
    mdast-util-to-hast: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  memoize-one@5.2.1:
    memoize-one: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-extension-math@3.1.0:
    micromark-extension-math: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-match@1.0.2:
    mime-match: private
  mime-types@2.1.35:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  ml-array-max@1.2.4:
    ml-array-max: private
  ml-array-min@1.2.3:
    ml-array-min: private
  ml-array-rescale@1.3.7:
    ml-array-rescale: private
  ml-matrix@6.12.1:
    ml-matrix: private
  mlly@1.7.4:
    mlly: private
  monaco-editor@0.52.2:
    monaco-editor: private
  motion-dom@11.18.1:
    motion-dom: private
  motion-utils@11.18.1:
    motion-utils: private
  mqtt-packet@9.0.2:
    mqtt-packet: private
  ms@2.1.3:
    ms: private
  mute-stream@2.0.0:
    mute-stream: private
  mz@2.7.0:
    mz: private
  namespace-emitter@2.0.1:
    namespace-emitter: private
  natural-compare@1.4.0:
    natural-compare: private
  neo-async@2.6.2:
    neo-async: private
  next-tick@1.1.0:
    next-tick: private
  no-case@3.0.4:
    no-case: private
  node-addon-api@7.1.1:
    node-addon-api: private
  node-html-parser@5.4.2:
    node-html-parser: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  normalize-range@0.1.2:
    normalize-range: private
  nth-check@2.1.1:
    nth-check: private
  number-allocator@1.0.14:
    number-allocator: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  on-finished@2.3.0:
    on-finished: private
  once@1.4.0:
    once: private
  open@8.4.2:
    open: private
  optionator@0.9.4:
    optionator: private
  outvariant@1.4.3:
    outvariant: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  package-manager-detector@1.3.0:
    package-manager-detector: private
  param-case@3.0.4:
    param-case: private
  parchment@1.1.4:
    parchment: private
  parent-module@1.0.1:
    parent-module: private
  parse-entities@2.0.0:
    parse-entities: private
  parse-json@5.2.0:
    parse-json: private
  parse5@7.3.0:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  pascal-case@3.1.2:
    pascal-case: private
  path-data-parser@0.1.0:
    path-data-parser: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@6.3.0:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  pathe@0.2.0:
    pathe: private
  pdfast@0.2.0:
    pdfast: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.7:
    pirates: private
  pkg-types@2.2.0:
    pkg-types: private
  points-on-curve@0.2.0:
    points-on-curve: private
  points-on-path@0.2.1:
    points-on-path: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-import@15.1.0(postcss@8.5.6):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.6):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.6):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.6):
    postcss-nested: private
  postcss-selector-parser@6.0.10:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  preact@10.26.9:
    preact: private
  prelude-ls@1.2.1:
    prelude-ls: private
  pretty-bytes@6.1.1:
    pretty-bytes: private
  prismjs@1.30.0:
    prismjs: private
  process-nextick-args@2.0.1:
    process-nextick-args: private
  process@0.11.10:
    process: private
  prop-types@15.8.1:
    prop-types: private
  property-information@7.1.0:
    property-information: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  psl@1.15.0:
    psl: private
  punycode@2.3.1:
    punycode: private
  quansync@0.2.10:
    quansync: private
  querystringify@2.2.0:
    querystringify: private
  queue-microtask@1.2.3:
    queue-microtask: private
  quickselect@2.0.0:
    quickselect: private
  quill-delta@5.1.0:
    quill-delta: private
  raf-schd@4.0.3:
    raf-schd: private
  randombytes@2.1.0:
    randombytes: private
  rbush@3.0.1:
    rbush: private
  rc-cascader@3.34.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-cascader: private
  rc-checkbox@3.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-checkbox: private
  rc-collapse@3.9.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-collapse: private
  rc-dialog@9.6.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-dialog: private
  rc-drawer@7.3.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-drawer: private
  rc-dropdown@4.2.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-dropdown: private
  rc-field-form@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-field-form: private
  rc-image@7.12.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-image: private
  rc-input-number@9.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-input-number: private
  rc-input@1.8.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-input: private
  rc-mentions@2.20.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-mentions: private
  rc-menu@9.16.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-menu: private
  rc-motion@2.9.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-motion: private
  rc-notification@5.6.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-notification: private
  rc-overflow@1.4.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-overflow: private
  rc-pagination@5.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-pagination: private
  rc-picker@4.11.3(dayjs@1.11.13)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-picker: private
  rc-progress@4.0.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-progress: private
  rc-rate@2.13.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-rate: private
  rc-resize-observer@1.4.3(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-resize-observer: private
  rc-segmented@2.7.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-segmented: private
  rc-select@14.16.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-select: private
  rc-slider@11.1.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-slider: private
  rc-steps@6.0.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-steps: private
  rc-switch@4.1.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-switch: private
  rc-table@7.51.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-table: private
  rc-tabs@15.6.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tabs: private
  rc-textarea@1.10.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-textarea: private
  rc-tooltip@6.4.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tooltip: private
  rc-tree-select@5.27.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tree-select: private
  rc-tree@5.13.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-tree: private
  rc-upload@4.9.2(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-upload: private
  rc-util@5.44.4(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-util: private
  rc-virtual-list@3.19.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    rc-virtual-list: private
  react-fast-compare@3.2.2:
    react-fast-compare: private
  react-is@16.13.1:
    react-is: private
  react-redux@7.2.9(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    react-redux: private
  react-refresh@0.17.0:
    react-refresh: private
  react-router@6.30.1(react@18.3.1):
    react-router: private
  read-cache@1.0.0:
    read-cache: private
  readable-stream@4.7.0:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  redux@4.2.1:
    redux: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  refractor@3.6.0:
    refractor: private
  regenerate-unicode-properties@10.2.0:
    regenerate-unicode-properties: private
  regenerate@1.4.2:
    regenerate: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  regexpu-core@6.2.0:
    regexpu-core: private
  regjsgen@0.8.0:
    regjsgen: private
  regjsparser@0.12.0:
    regjsparser: private
  relateurl@0.2.7:
    relateurl: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-rehype@11.1.2:
    remark-rehype: private
  remark-stringify@11.0.0:
    remark-stringify: private
  require-directory@2.1.1:
    require-directory: private
  require-from-string@2.0.2:
    require-from-string: private
  requires-port@1.0.0:
    requires-port: private
  resize-observer-polyfill@1.5.1:
    resize-observer-polyfill: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve@1.22.10:
    resolve: private
  reusify@1.1.0:
    reusify: private
  rfdc@1.4.1:
    rfdc: private
  robust-predicates@3.0.2:
    robust-predicates: private
  rollup-plugin-external-globals@0.6.1(rollup@4.45.1):
    rollup-plugin-external-globals: private
  rollup@4.45.1:
    rollup: private
  roughjs@4.6.6:
    roughjs: private
  run-parallel@1.2.0:
    run-parallel: private
  rw@1.3.3:
    rw: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.23.2:
    scheduler: private
  schema-utils@4.3.2:
    schema-utils: private
  screenfull@5.2.0:
    screenfull: private
  scroll-into-view-if-needed@3.1.0:
    scroll-into-view-if-needed: private
  semver@6.3.1:
    semver: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  slate-history@0.66.0(slate@0.72.8):
    slate-history: private
  slate@0.72.8:
    slate: private
  smart-buffer@4.2.0:
    smart-buffer: private
  smob@1.5.0:
    smob: private
  snabbdom@3.6.2:
    snabbdom: private
  socks@2.8.6:
    socks: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.7.4:
    source-map: private
  sourcemap-codec@1.4.8:
    sourcemap-codec: private
  space-separated-tokens@2.0.2:
    space-separated-tokens: private
  split.js@1.6.5:
    split.js: private
  split2@4.2.0:
    split2: private
  sprintf-js@1.1.3:
    sprintf-js: private
  ssr-window@3.0.0:
    ssr-window: private
  state-local@1.0.7:
    state-local: private
  statuses@2.0.2:
    statuses: private
  stop-iteration-iterator@1.1.0:
    stop-iteration-iterator: private
  strict-event-emitter@0.5.1:
    strict-event-emitter: private
  string-convert@0.2.1:
    string-convert: private
  string-width@4.2.3:
    string-width: private
    string-width-cjs: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  stringify-entities@4.0.4:
    stringify-entities: private
  stringify-object@3.3.0:
    stringify-object: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-comments@2.0.1:
    strip-comments: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  style-to-js@1.1.17:
    style-to-js: private
  style-to-object@1.0.9:
    style-to-object: private
  stylis@4.3.6:
    stylis: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg-path-parser@1.1.0:
    svg-path-parser: private
  tapable@2.2.2:
    tapable: private
  temp-dir@2.0.0:
    temp-dir: private
  tempy@0.6.0:
    tempy: private
  terser-webpack-plugin@5.3.14(esbuild@0.21.5)(webpack@5.100.2(esbuild@0.21.5)):
    terser-webpack-plugin: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  throttle-debounce@5.0.2:
    throttle-debounce: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tiny-warning@1.0.3:
    tiny-warning: private
  tinyexec@1.0.1:
    tinyexec: private
  tinyglobby@0.2.14:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toggle-selection@1.0.6:
    toggle-selection: private
  tough-cookie@4.1.4:
    tough-cookie: private
  tr46@1.0.1:
    tr46: private
  trim-lines@3.0.1:
    trim-lines: private
  trough@2.2.0:
    trough: private
  ts-api-utils@2.1.0(typescript@5.8.3):
    ts-api-utils: private
  ts-dedent@2.2.0:
    ts-dedent: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tslib@2.8.1:
    tslib: private
  tsparticles-basic@2.12.0:
    tsparticles-basic: private
  tsparticles-engine@2.12.0:
    tsparticles-engine: private
  tsparticles-interaction-external-attract@2.12.0:
    tsparticles-interaction-external-attract: private
  tsparticles-interaction-external-bounce@2.12.0:
    tsparticles-interaction-external-bounce: private
  tsparticles-interaction-external-bubble@2.12.0:
    tsparticles-interaction-external-bubble: private
  tsparticles-interaction-external-connect@2.12.0:
    tsparticles-interaction-external-connect: private
  tsparticles-interaction-external-grab@2.12.0:
    tsparticles-interaction-external-grab: private
  tsparticles-interaction-external-pause@2.12.0:
    tsparticles-interaction-external-pause: private
  tsparticles-interaction-external-push@2.12.0:
    tsparticles-interaction-external-push: private
  tsparticles-interaction-external-remove@2.12.0:
    tsparticles-interaction-external-remove: private
  tsparticles-interaction-external-repulse@2.12.0:
    tsparticles-interaction-external-repulse: private
  tsparticles-interaction-external-slow@2.12.0:
    tsparticles-interaction-external-slow: private
  tsparticles-interaction-particles-attract@2.12.0:
    tsparticles-interaction-particles-attract: private
  tsparticles-interaction-particles-collisions@2.12.0:
    tsparticles-interaction-particles-collisions: private
  tsparticles-interaction-particles-links@2.12.0:
    tsparticles-interaction-particles-links: private
  tsparticles-move-base@2.12.0:
    tsparticles-move-base: private
  tsparticles-move-parallax@2.12.0:
    tsparticles-move-parallax: private
  tsparticles-particles.js@2.12.0:
    tsparticles-particles.js: private
  tsparticles-plugin-easing-quad@2.12.0:
    tsparticles-plugin-easing-quad: private
  tsparticles-shape-circle@2.12.0:
    tsparticles-shape-circle: private
  tsparticles-shape-image@2.12.0:
    tsparticles-shape-image: private
  tsparticles-shape-line@2.12.0:
    tsparticles-shape-line: private
  tsparticles-shape-polygon@2.12.0:
    tsparticles-shape-polygon: private
  tsparticles-shape-square@2.12.0:
    tsparticles-shape-square: private
  tsparticles-shape-star@2.12.0:
    tsparticles-shape-star: private
  tsparticles-shape-text@2.12.0:
    tsparticles-shape-text: private
  tsparticles-updater-color@2.12.0:
    tsparticles-updater-color: private
  tsparticles-updater-life@2.12.0:
    tsparticles-updater-life: private
  tsparticles-updater-opacity@2.12.0:
    tsparticles-updater-opacity: private
  tsparticles-updater-out-modes@2.12.0:
    tsparticles-updater-out-modes: private
  tsparticles-updater-rotate@2.12.0:
    tsparticles-updater-rotate: private
  tsparticles-updater-size@2.12.0:
    tsparticles-updater-size: private
  tsparticles-updater-stroke-color@2.12.0:
    tsparticles-updater-stroke-color: private
  type-check@0.4.0:
    type-check: private
  type-fest@4.41.0:
    type-fest: private
  type@2.7.3:
    type: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  typedarray@0.0.6:
    typedarray: private
  ufo@1.6.1:
    ufo: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.21.0:
    undici-types: private
  unicode-canonical-property-names-ecmascript@2.0.1:
    unicode-canonical-property-names-ecmascript: private
  unicode-match-property-ecmascript@2.0.0:
    unicode-match-property-ecmascript: private
  unicode-match-property-value-ecmascript@2.2.0:
    unicode-match-property-value-ecmascript: private
  unicode-property-aliases-ecmascript@2.1.0:
    unicode-property-aliases-ecmascript: private
  unified@11.0.5:
    unified: private
  unique-string@2.0.0:
    unique-string: private
  unist-util-find-after@5.0.0:
    unist-util-find-after: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-position@5.0.0:
    unist-util-position: private
  unist-util-remove-position@5.0.0:
    unist-util-remove-position: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  universalify@2.0.1:
    universalify: private
  unpipe@1.0.0:
    unpipe: private
  upath@1.2.0:
    upath: private
  update-browserslist-db@1.1.3(browserslist@4.25.1):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  url-parse@1.5.10:
    url-parse: private
  use-memo-one@1.1.3(react@18.3.1):
    use-memo-one: private
  use-merge-value@1.2.0(react@18.3.1):
    use-merge-value: private
  use-sync-external-store@1.5.0(react@18.3.1):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  util@0.10.4:
    util: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@11.1.0:
    uuid: private
  vfile-location@5.0.3:
    vfile-location: private
  vfile-message@4.0.2:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  void-elements@3.1.0:
    void-elements: private
  vscode-jsonrpc@8.2.0:
    vscode-jsonrpc: private
  vscode-languageserver-protocol@3.17.5:
    vscode-languageserver-protocol: private
  vscode-languageserver-textdocument@1.0.12:
    vscode-languageserver-textdocument: private
  vscode-languageserver-types@3.17.5:
    vscode-languageserver-types: private
  vscode-languageserver@9.0.1:
    vscode-languageserver: private
  vscode-uri@3.0.8:
    vscode-uri: private
  watchpack@2.4.4:
    watchpack: private
  web-namespaces@2.0.1:
    web-namespaces: private
  webidl-conversions@4.0.2:
    webidl-conversions: private
  webpack-sources@3.3.3:
    webpack-sources: private
  webpack@5.100.2(esbuild@0.21.5):
    webpack: private
  whatwg-url@7.1.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  wildcard@1.1.2:
    wildcard: private
  word-wrap@1.2.5:
    word-wrap: private
  workbox-background-sync@7.3.0:
    workbox-background-sync: private
  workbox-broadcast-update@7.3.0:
    workbox-broadcast-update: private
  workbox-build@7.3.0(@types/babel__core@7.20.5):
    workbox-build: private
  workbox-cacheable-response@7.3.0:
    workbox-cacheable-response: private
  workbox-core@7.3.0:
    workbox-core: private
  workbox-expiration@7.3.0:
    workbox-expiration: private
  workbox-google-analytics@7.3.0:
    workbox-google-analytics: private
  workbox-navigation-preload@7.3.0:
    workbox-navigation-preload: private
  workbox-precaching@7.3.0:
    workbox-precaching: private
  workbox-range-requests@7.3.0:
    workbox-range-requests: private
  workbox-recipes@7.3.0:
    workbox-recipes: private
  workbox-routing@7.3.0:
    workbox-routing: private
  workbox-strategies@7.3.0:
    workbox-strategies: private
  workbox-streams@7.3.0:
    workbox-streams: private
  workbox-sw@7.3.0:
    workbox-sw: private
  workbox-window@7.3.0:
    workbox-window: private
  worker-timers-broker@6.1.8:
    worker-timers-broker: private
  worker-timers-worker@7.0.71:
    worker-timers-worker: private
  worker-timers@7.1.8:
    worker-timers: private
  workerize-loader@2.0.2(webpack@5.100.2(esbuild@0.21.5)):
    workerize-loader: private
  wrap-ansi@6.2.0:
    wrap-ansi: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrappy@1.0.2:
    wrappy: private
  ws@8.18.3:
    ws: private
  xtend@4.0.2:
    xtend: private
  y18n@5.0.8:
    y18n: private
  yallist@3.1.1:
    yallist: private
  yaml@2.8.0:
    yaml: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
  yoctocolors-cjs@2.1.2:
    yoctocolors-cjs: private
  zustand@4.5.7(@types/react@18.3.23)(immer@9.0.21)(react@18.3.1):
    zustand: private
  zwitch@2.0.4:
    zwitch: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.11.0
pendingBuilds: []
prunedAt: Thu, 31 Jul 2025 00:58:10 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@parcel/watcher-android-arm64@2.5.1'
  - '@parcel/watcher-darwin-arm64@2.5.1'
  - '@parcel/watcher-darwin-x64@2.5.1'
  - '@parcel/watcher-freebsd-x64@2.5.1'
  - '@parcel/watcher-linux-arm-glibc@2.5.1'
  - '@parcel/watcher-linux-arm-musl@2.5.1'
  - '@parcel/watcher-linux-arm64-glibc@2.5.1'
  - '@parcel/watcher-linux-arm64-musl@2.5.1'
  - '@parcel/watcher-linux-x64-glibc@2.5.1'
  - '@parcel/watcher-linux-x64-musl@2.5.1'
  - '@parcel/watcher-win32-arm64@2.5.1'
  - '@parcel/watcher-win32-ia32@2.5.1'
  - '@rollup/rollup-android-arm-eabi@4.45.1'
  - '@rollup/rollup-android-arm64@4.45.1'
  - '@rollup/rollup-darwin-arm64@4.45.1'
  - '@rollup/rollup-darwin-x64@4.45.1'
  - '@rollup/rollup-freebsd-arm64@4.45.1'
  - '@rollup/rollup-freebsd-x64@4.45.1'
  - '@rollup/rollup-linux-arm-gnueabihf@4.45.1'
  - '@rollup/rollup-linux-arm-musleabihf@4.45.1'
  - '@rollup/rollup-linux-arm64-gnu@4.45.1'
  - '@rollup/rollup-linux-arm64-musl@4.45.1'
  - '@rollup/rollup-linux-loongarch64-gnu@4.45.1'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.45.1'
  - '@rollup/rollup-linux-riscv64-gnu@4.45.1'
  - '@rollup/rollup-linux-riscv64-musl@4.45.1'
  - '@rollup/rollup-linux-s390x-gnu@4.45.1'
  - '@rollup/rollup-linux-x64-gnu@4.45.1'
  - '@rollup/rollup-linux-x64-musl@4.45.1'
  - '@rollup/rollup-win32-arm64-msvc@4.45.1'
  - '@rollup/rollup-win32-ia32-msvc@4.45.1'
  - '@swc/core-darwin-arm64@1.13.1'
  - '@swc/core-darwin-x64@1.13.1'
  - '@swc/core-linux-arm-gnueabihf@1.13.1'
  - '@swc/core-linux-arm64-gnu@1.13.1'
  - '@swc/core-linux-arm64-musl@1.13.1'
  - '@swc/core-linux-x64-gnu@1.13.1'
  - '@swc/core-linux-x64-musl@1.13.1'
  - '@swc/core-win32-arm64-msvc@1.13.1'
  - '@swc/core-win32-ia32-msvc@1.13.1'
  - fsevents@2.3.3
storeDir: C:\Users\<USER>\.hvigor\caches\v10
virtualStoreDir: C:\Users\<USER>\Desktop\backup\react-admin-antd-master\node_modules\.pnpm
virtualStoreDirMaxLength: 60
