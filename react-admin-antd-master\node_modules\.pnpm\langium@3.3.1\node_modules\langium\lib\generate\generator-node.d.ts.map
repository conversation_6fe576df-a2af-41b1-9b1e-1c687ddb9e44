{"version": 3, "file": "generator-node.d.ts", "sourceRoot": "", "sources": ["../../src/generate/generator-node.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAEhF,OAAO,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,mBAAmB,CAAC;AAE7D,OAAO,KAAK,EAAE,YAAY,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAIzF,eAAO,MAAM,GAAG,QAA2F,CAAC;AAE5G;;;;;GAKG;AACH,MAAM,MAAM,SAAS,GAAG,aAAa,GAAG,MAAM,GAAG,SAAS,CAAC;AAC3D,MAAM,MAAM,aAAa,GAAG,sBAAsB,GAAG,UAAU,GAAG,WAAW,CAAC;AAE9E,MAAM,WAAW,YAAY;IACzB,gBAAgB,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,KAAK,IAAI,CAAC,CAAC;IAClE,WAAW,CAAC,EAAE,MAAM,GAAG,MAAM,CAAC;IAC9B,gBAAgB,CAAC,EAAE,OAAO,CAAC;IAC3B,iBAAiB,CAAC,EAAE,OAAO,CAAC;CAC/B;AAED,wBAAgB,eAAe,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,IAAI,sBAAsB,GAAG,UAAU,GAAG,WAAW,CAIxG;AAED,wBAAgB,aAAa,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,IAAI,WAAW,CAEhE;AAED;;;;;;;;GAQG;AACH,wBAAgB,QAAQ,CAAC,KAAK,EAAE,OAAO,EAAE,kBAAkB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,MAAM,CAKrF;AAED;;;;;;;;GAQG;AACH,wBAAgB,gBAAgB,CAAC,KAAK,EAAE,aAAa,EAAE,kBAAkB,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG;IAAE,IAAI,EAAE,MAAM,CAAC;IAAC,KAAK,EAAE,WAAW,CAAA;CAAE,CAEjI;AAED;;;;;GAKG;AACH,qBAAa,sBAAsB;IAE/B,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,aAAa,GAAG,MAAM,CAAC,CAAC,CAAM;IAExD,YAAY,CAAC,EAAE,eAAe,GAAG,YAAY,GAAG,YAAY,EAAE,CAAC;IAE/D;;;;;;;;;;OAUG;gBACS,GAAG,OAAO,EAAE,SAAS,EAAE;IAInC,OAAO,IAAI,OAAO;IAIlB;;;;;;;;;;;;;OAaG;IACH,KAAK,CAAC,CAAC,SAAS,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI;IAEpE;;;;;;;;;;;;;;;;OAgBG;IACH,KAAK,CAAC,CAAC,SAAS,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,SAAS,GAAG,IAAI;IAE9F;;;;;;;;;OASG;IACH,KAAK,CAAC,YAAY,EAAE,YAAY,GAAG,SAAS,GAAG,IAAI;IAEnD;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,YAAY,EAAE,YAAY,EAAE,GAAG,IAAI;IAczC;;;;;;;;;;;;;;OAcG;IACH,MAAM,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI;IAWnE;;;;;;;;;;;;;;;;;;OAkBG;IACH,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI;IAI3G;;;;;;;;;;OAUG;IACH,aAAa,IAAI,IAAI;IAIrB;;;;;;;;;;;;;;OAcG;IACH,eAAe,CAAC,SAAS,EAAE,OAAO,GAAG,IAAI;IAIzC;;;;;;;;;;;OAWG;IACH,uBAAuB,IAAI,IAAI;IAI/B;;;;;;;;;;;;;;;OAeG;IACH,yBAAyB,CAAC,SAAS,EAAE,OAAO,GAAG,IAAI;IAInD;;;;;;;;;;;OAWG;IACH,cAAc,CAAC,WAAW,EAAE,oBAAoB,EAAE,GAAG,aAAa,EAAE,OAAO,EAAE,GAAG,IAAI;IAMpF;;;;;;;;;;;;;;;;;;;OAmBG;IACH,gBAAgB,CAAC,SAAS,EAAE,OAAO,GAAG,CAAC,WAAW,EAAE,oBAAoB,EAAE,GAAG,aAAa,EAAE,OAAO,EAAE,KAAK,IAAI;IAI9G;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8BG;IACH,MAAM,CAAC,gBAAgB,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE,UAAU,KAAK,IAAI,CAAC,GAAG,YAAY,GAAI,IAAI;IAiB/F;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,CAAC,KAAK,IAAI;IAExJ;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,YAAY,CAAC,CAAC,SAAS,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,SAAS,GAAG,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,CAAC,KAAK,IAAI;IAElL;;;;;;;;;;;;;;;;;;OAkBG;IACH,YAAY,CAAC,YAAY,EAAE,YAAY,GAAG,SAAS,GAAG,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,CAAC,KAAK,IAAI;IAEvI;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,YAAY,CAAC,aAAa,EAAE,YAAY,EAAE,GAAG,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,CAAC,KAAK,IAAI;IAW9H;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,cAAc,CAAC,CAAC,SAAS,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,CAAC,KAAK,IAAI;IAE9K;;;;;;;;;;;;;;;;;;;;;;;;;OAyBG;IACH,cAAc,CAAC,CAAC,SAAS,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,SAAS,GAAG,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,CAAC,KAAK,IAAI;IAExM;;;;;;;;;;;;;;;;;;;;;;OAsBG;IACH,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,GAAG,SAAS,GAAG,CAAC,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,CAAC,KAAK,IAAI;IAEhM;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,cAAc,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAC,MAAM,YAAY,EAAE,CAAC,GAAG,CAAC,GAAG,OAAO,EAAE,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,CAAC,KAAK,IAAI;IAO7K;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,oBAAoB,CAAC,CAAC,SAAS,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,oBAAoB,EAAE,GAAG,aAAa,EAAE,OAAO,EAAE,KAAK,IAAI;IAEvJ;;;;;;;;;;;;;;;;;;;;;;;OAuBG;IACH,oBAAoB,CAAC,CAAC,SAAS,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,SAAS,GAAG,CAAC,WAAW,EAAE,oBAAoB,EAAE,GAAG,aAAa,EAAE,OAAO,EAAE,KAAK,IAAI;IAEjL;;;;;;;;;;;;;;;;;;OAkBG;IACH,oBAAoB,CAAC,YAAY,EAAE,YAAY,GAAG,SAAS,GAAG,CAAC,WAAW,EAAE,oBAAoB,EAAE,GAAG,aAAa,EAAE,OAAO,EAAE,KAAK,IAAI;IAEtI;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,oBAAoB,CAAC,aAAa,EAAE,YAAY,EAAE,GAAG,CAAC,WAAW,EAAE,oBAAoB,EAAE,GAAG,aAAa,EAAE,OAAO,EAAE,KAAK,IAAI;IAW7H;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,sBAAsB,CAAC,CAAC,SAAS,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,WAAW,EAAE,oBAAoB,EAAE,GAAG,aAAa,EAAE,OAAO,EAAE,KAAK,IAAI;IAE7K;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACH,sBAAsB,CAAC,CAAC,SAAS,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,SAAS,GAAG,CAAC,WAAW,EAAE,oBAAoB,EAAE,GAAG,aAAa,EAAE,OAAO,EAAE,KAAK,IAAI;IAEvM;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,sBAAsB,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,GAAG,SAAS,GAAG,CAAC,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,WAAW,EAAE,oBAAoB,EAAE,GAAG,aAAa,EAAE,OAAO,EAAE,KAAK,IAAI;IAE/L;;;;;;;;;;;;;;;;;;;;;;;;;;OA0BG;IACH,sBAAsB,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAC,MAAM,YAAY,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,oBAAoB,EAAE,GAAG,aAAa,EAAE,OAAO,EAAE,KAAK,IAAI;CAM/K;AAED;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,wBAAgB,WAAW,CAAC,CAAC,SAAS,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,KAAK,sBAAsB,CAAC;AAEjL;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,wBAAgB,WAAW,CAAC,CAAC,SAAS,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,SAAS,GAAG,CAAC,OAAO,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,KAAK,sBAAsB,CAAC;AAE3M;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,wBAAgB,WAAW,CAAC,YAAY,EAAE,YAAY,GAAG,SAAS,GAAG,CAAC,OAAO,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,KAAK,sBAAsB,CAAC;AAEhK;;;;;;;;;;;;;;;;;;;;;;;;;GAyBG;AACH,wBAAgB,WAAW,CAAC,aAAa,EAAE,YAAY,EAAE,GAAG,CAAC,OAAO,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,KAAK,sBAAsB,CAAC;AAevJ;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,wBAAgB,aAAa,CAAC,CAAC,SAAS,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,KAAK,sBAAsB,GAAG,SAAS,CAAC;AAEnN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,wBAAgB,aAAa,CAAC,CAAC,SAAS,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,QAAQ,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,MAAM,GAAG,SAAS,GAAG,CAAC,OAAO,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,KAAK,sBAAsB,GAAG,SAAS,CAAC;AAE7O;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,wBAAgB,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,GAAG,SAAS,GAAG,CAAC,MAAM,YAAY,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,KAAK,sBAAsB,GAAG,SAAS,CAAC;AAErO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,wBAAgB,aAAa,CAAC,SAAS,EAAE,OAAO,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAC,MAAM,YAAY,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,SAAS,GAAG,CAAC,CAAC,IAAI,EAAE,sBAAsB,KAAK,IAAI,CAAC,KAAK,sBAAsB,GAAG,SAAS,CAAC;AAQlN;;GAEG;AACH,qBAAa,UAAW,SAAQ,sBAAsB;IAElD,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,iBAAiB,UAAQ;IACzB,gBAAgB,UAAS;gBAEb,WAAW,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,iBAAiB,UAAO,EAAE,gBAAgB,UAAQ;CAUhG;AAED;;GAEG;AACH,qBAAa,WAAW;IAEpB,aAAa,EAAE,MAAM,CAAC;IAEtB,UAAU,UAAS;gBAEP,aAAa,CAAC,EAAE,MAAM,EAAE,UAAU,UAAQ;CAIzD;AAED,eAAO,MAAM,EAAE,aAAoB,CAAC;AACpC,eAAO,MAAM,OAAO,aAAmC,CAAC"}