{"version": 3, "file": "generator-tracing.js", "sourceRoot": "", "sources": ["../../src/generate/generator-tracing.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAKhF,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpD,OAAO,EAAE,oBAAoB,EAAE,MAAM,2BAA2B,CAAC;AACjE,OAAO,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;AAyCpD,MAAM,UAAU,eAAe,CAAC,UAAuE;;IACnG,IAAI,CAAC,UAAU,EAAE,CAAC;QACd,OAAO,SAAS,CAAC;IAErB,CAAC;SAAM,IAAI,SAAS,IAAI,UAAU,EAAE,CAAC;QACjC,OAAO,wBAAwB,CAAC,UAAU,CAAC,CAAC;IAEhD,CAAC;SAAM,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;QACnC,OAAO,UAAU,CAAC,MAAM,CAAE,oBAAoB,EAAE,SAAS,CAAE,CAAC,CAAC,iHAAiH;IAElL,CAAC;SAAM,CAAC;QACJ,yFAAyF;QACzF,wGAAwG;QACxG,uEAAuE;QACvE,yBAAyB;QACzB,oDAAoD;QACpD,mDAAmD;QACnD,uCAAuC;QAEvC,MAAM,YAAY,GAAwB,UAAU,CAAC;QAErD,MAAM,uBAAuB,GAAG,SAAS,CAAC,YAAY,CAAC;YACnD,CAAC,CAAC,yBAAyB,CAAC,MAAA,MAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,IAAI,0CAAE,OAAO,mCAAI,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAElG,OAAO,mBAAmB,CAAC,YAAY,EAAE,uBAAuB,CAAC,CAAC;IACtE,CAAC;AACL,CAAC;AAED,SAAS,SAAS,CAAC,OAA2B;IAC1C,OAAO,OAAO,OAAO,KAAK,WAAW,IAAK,SAAS,IAAI,OAAO,IAAI,MAAM,IAAI,OAAO,CAAC;AACxF,CAAC;AAED,SAAS,yBAAyB,CAAC,OAAgB;IAC/C,IAAI,CAAC;QACD,OAAO,WAAW,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC;IAC/C,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACT,OAAO,SAAS,CAAC;IACrB,CAAC;AACL,CAAC;AAED,SAAS,wBAAwB,CAAC,UAA2B;;IACzD,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,UAAU,aAAV,UAAU,cAAV,UAAU,GAAI,EAAE,CAAC;IACtD,MAAM,UAAU,GAA6C,MAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,mCAAK,OAAiC,aAAjC,OAAO,uBAAP,OAAO,CAA4B,WAAW,CAAC;IAElI,IAAI,OAAO,KAAK,SAAS,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;QACpD,OAAO,SAAS,CAAC;IAErB,CAAC;SAAM,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;QAChC,OAAO,mBAAmB,CAAC,UAAU,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;IAEpE,CAAC;SAAM,CAAC;QACJ,MAAM,yBAAyB,GAAG,CAAC,OAAuB,EAA4B,EAAE;YACpF,IAAI,KAAK,KAAK,SAAS,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,OAAO,CAAE,OAA0B,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;gBAC5F,OAAO,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;YAE/D,CAAC;iBAAM,CAAC;gBACJ,OAAO,OAAO,CAAC,MAAM,CAAC,oBAAoB,EAAE,SAAS,CAAC,CAAC;YAC3D,CAAC;QACL,CAAC,CAAC;QAEF,IAAI,MAAA,UAAU,CAAC,WAAW,0CAAG,QAAQ,CAAC,EAAE,CAAC;YACrC,MAAM,MAAM,GAAG,yBAAyB,CACpC,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CACnC,CAAC;YACF,OAAO,MAAM,IAAI,mBAAmB,CAAC,MAAM,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;QAE1E,CAAC;aAAM,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;YAC1B,MAAM,MAAM,GAAG,yBAAyB,CACpC,oBAAoB,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CACnD,CAAC;YACF,OAAO,MAAM,IAAI,mBAAmB,CAAC,MAAM,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC;QAE1E,CAAC;aAAM,CAAC;YACJ,OAAO,SAAS,CAAC;QACrB,CAAC;IACL,CAAC;AACL,CAAC;AAED,SAAS,cAAc,CAAC,OAA8B;;IAClD,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QACnB,OAAO,MAAA,MAAA,WAAW,CAAC,OAAO,CAAC,0CAAE,GAAG,0CAAE,QAAQ,EAAE,CAAC;IAEjD,CAAC;SAAM,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;QAC7B,OAAO,OAAO,CAAC,WAAW,CAAC,WAAW;gBAC/B,MAAA,MAAA,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC,UAAU,CAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAE,CAAC,CAAC,EAAE,WAAC,OAAA,MAAA,CAAC,CAAC,WAAW,0CAAE,WAAW,CAAA,EAAA,CAAE,0CAAE,WAAW,0CAAE,WAAW,CAAA,CAAC;IAEpJ,CAAC;SAAM,CAAC;QACJ,OAAO,SAAS,CAAC;IACrB,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAAC,MAA2B,EAAE,OAAgB;;IACtE,MAAM,MAAM,GAA+B;QACvC,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,GAAG,EAAK,MAAA,MAAM,CAAC,GAAG,mCAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAO;QACpD,MAAM,EAAE,MAAA,MAAM,CAAC,MAAM,mCAAI,MAAM,CAAC,GAAI,GAAG,MAAM,CAAC,MAAM;KACvD,CAAC;IAEF,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;IAChC,CAAC;IAED,OAAO,aAAP,OAAO,cAAP,OAAO,IAAP,OAAO,GAAK,MAAM,CAAC,OAAO,EAAC;IAC3B,IAAI,OAAO,EAAE,CAAC;QACV,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAS,oBAAoB,CAAC,IAAqC,EAAE,IAAyB;;IAC1F,IAAI,CAAC,IAAI,EAAE,CAAC;QACR,OAAO,IAAI,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;SAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QACf,OAAO,IAAI,IAAI,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,MAAM,OAAO,GAAG,MAAA,IAAI,CAAC,GAAG,mCAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAO,CAAC;IACvD,MAAM,OAAO,GAAG,MAAA,IAAI,CAAC,GAAG,mCAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAO,CAAC;IACvD,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAClD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;IACvC,MAAM,MAAM,GAAG,GAAG,GAAG,MAAM,CAAC;IAE5B,MAAM,MAAM,GAA+B;QACvC,MAAM,EAAE,GAAG,EAAE,MAAM;KACtB,CAAC;IAEF,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;QAC3B,MAAM,CAAC,KAAK,GAAU;YAClB,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI;mBACzC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS;gBACjH,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK;YACzC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI;mBACnC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS;gBACzG,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;SACxC,CAAC;IACN,CAAC;IAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC7B,MAAM,OAAO,GAAG,OAAO,IAAI,OAAO,IAAI,OAAO,KAAK,OAAO,CAAC,CAAC,CAAC,+BAA+B,OAAO,KAAK,OAAO,GAAG,CAAC,CAAC,CAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,OAAO,CAAC;QACvI,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;IAC7B,CAAC;IACD,OAAO,MAAM,CAAC;AAClB,CAAC"}