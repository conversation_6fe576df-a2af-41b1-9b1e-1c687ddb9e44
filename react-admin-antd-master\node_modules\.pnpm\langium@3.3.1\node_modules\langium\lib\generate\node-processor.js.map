{"version": 3, "file": "node-processor.js", "sourceRoot": "", "sources": ["../../src/generate/node-processor.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAKhF,OAAO,EAAE,sBAAsB,EAAE,UAAU,EAAE,WAAW,EAAE,MAAM,qBAAqB,CAAC;AACtF,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAIzD,MAAM,OAAO;IAYT,YAAY,aAA+B;QAV3C,uBAAkB,GAAG,MAAM,CAAC;QAC5B,kBAAa,GAAG,IAAI,CAAC;QACZ,mBAAc,GAAiB,EAAE,CAAC;QAClC,8BAAyB,GAAiB,EAAE,CAAC;QAE9C,cAAS,GAA0B,EAAE,CAAC;QAEtC,UAAK,GAAe,CAAC,EAAE,CAAC,CAAC;QACzB,WAAM,GAAW,CAAC,CAAC;QAGvB,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;YACpC,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC;QAC5C,CAAC;aAAM,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;YAC3C,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QACzD,CAAC;IACL,CAAC;IAED,IAAI,OAAO;QACP,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB,CAAC;IAED,IAAI,iBAAiB;QACjB,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;IACjC,CAAC;IAED,IAAI,kBAAkB;QAClB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACvD,CAAC;IAED,IAAI,eAAe;QACf,OAAO;YACH,MAAM,EAAE,IAAI,CAAC,aAAa;YAC1B,IAAI,EAAE,IAAI,CAAC,iBAAiB;YAC5B,SAAS,EAAE,IAAI,CAAC,kBAAkB,CAAC,MAAM;SAC5C,CAAC;IACN,CAAC;IAED,MAAM,CAAC,KAAa,EAAE,QAAkB;QACpC,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACnB,MAAM,SAAS,GAAG,QAAQ,IAAI,IAAI,CAAC,eAAe,CAAC;YACnD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC/C,IAAI,CAAC,MAAM,IAAI,KAAK,CAAC,MAAM,CAAC;YAC5B,IAAI,SAAS,EAAE,CAAC;gBACZ,IAAI,CAAC,yBAAyB,CAAC,SAAS,CAAC,CAAC;YAC9C,CAAC;QACL,CAAC;IACL,CAAC;IAEO,yBAAyB,CAAC,MAAyB;QACvD,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,MAAM,EAAE,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;YAC7B,IAAI,EAAE,CAAC,WAAW,IAAI,EAAE,CAAC,WAAW,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,CAAC,wFAAwF;gBAClJ,EAAE,CAAC,WAAW,GAAG,IAAI,CAAC,eAAe,CAAC;QAC9C,CAAC;IACL,CAAC;IAED,cAAc,CAAC,IAAgB;QAC3B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC;YAC1B,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;IAED,cAAc;QACV,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;IAC9B,CAAC;IAED,IAAI,eAAe;QACf,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACxF,CAAC;IAED,gBAAgB;QACZ,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC;QAClE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;QACxC,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC9B,CAAC;IAED,UAAU;QACN,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpB,IAAI,CAAC,yBAAyB,CAAC,MAAM,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED,eAAe,CAAC,YAAsC;QAClD,MAAM,MAAM,GAAG,iBAAiB,CAC5B,YAAY,EACZ,IAAI,CAAC,eAAe,EACpB,EAAE,CAAC,EAAE,eAAC,OAAA,MAAA,MAAA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,0CAAE,QAAQ,0CAAE,IAAI,CAAC,EAAE,CAAC,CAAA,EAAA,CAAC,CAAC;QACzE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC5B,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,cAAc,CAAC,QAAqB;QAChC,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,EAAG,CAAC;QAC1C,+EAA+E;QAC/E,IAAI,CAAC,UAAU,CAAC,WAAW,KAAK,QAAQ,EAAE,wBAAwB,CAAC,CAAC;QAEpE,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,2BAA2B;;QACvB,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAClD,MAAM,OAAO,GAAG,MAAA,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,YAAY,0CAAE,OAAO,CAAC;YACxD,IAAI,OAAO;gBACP,OAAO,OAAO,CAAC;QACvB,CAAC;QACD,OAAO,SAAS,CAAC;IACrB,CAAC;IAEO,UAAU,CAAC,SAAkB,EAAE,GAAW;QAC9C,IAAI,CAAC,SAAS,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC;IACL,CAAC;CACJ;AAOD,SAAS,iBAAiB,CAAC,YAAsC,EAAE,WAA8B,EAAE,MAAiC;IAChI,MAAM,MAAM,GAAwB;QAChC,YAAY;QACZ,YAAY,EAAE,SAAU;QACxB,QAAQ,EAAE,EAAE;QACZ,WAAW;QACX,QAAQ,EAAE,CAAC,SAA4B,EAAE,EAAE;;YACvC,MAAM,CAAC,YAAY,GAAe;gBAC9B,MAAM,EAAE,MAAM,CAAC,WAAY,CAAC,MAAM;gBAClC,GAAG,EAAE,SAAS,CAAC,MAAM;gBACrB,MAAM,EAAE,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,WAAY,CAAC,MAAM;gBACrD,KAAK,EAAS;oBACV,KAAK,EAAE;wBACH,IAAI,EAAE,MAAM,CAAC,WAAY,CAAC,IAAI;wBAC9B,SAAS,EAAE,MAAM,CAAC,WAAY,CAAC,SAAS;qBAC3C;oBACD,GAAG,EAAE;wBACD,IAAI,EAAE,SAAS,CAAC,IAAI;wBACpB,SAAS,EAAE,SAAS,CAAC,SAAS;qBACjC;iBACJ;aACJ,CAAC;YACF,OAAO,MAAM,CAAC,WAAW,CAAC;YAC1B,IAAI,CAAA,MAAA,MAAM,CAAC,QAAQ,0CAAE,MAAM,MAAK,CAAC,EAAE,CAAC;gBAChC,OAAO,MAAM,CAAC,QAAQ,CAAC;YAC3B,CAAC;YACD,IAAI,MAAA,MAAM,CAAC,YAAY,0CAAE,MAAM,EAAE,CAAC;gBAC9B,MAAM,CAAC,MAAM,CAAC,CAAC;YACnB,CAAC;YACD,OAAO,MAAM,CAAC,QAAQ,CAAC;YACvB,OAAO,MAAM,CAAC;QAClB,CAAC;KACJ,CAAC;IACF,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,IAAmB,EAAE,kBAAoC;IAC1F,MAAM,OAAO,GAAG,IAAI,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAChD,MAAM,KAAK,GAAG,OAAO,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;IAEjD,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAEnC,OAAO,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;IAC9B,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAE1D,MAAM,WAAW,GAAG,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAClG,MAAM,uBAAuB,GAAG,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,YAAY,CAAC;IAC1D,MAAM,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC;IAE5C,IAAI,uBAAuB,IAAI,WAAW,CAAC,YAAY;WAC5C,uBAAuB,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM;WAC1D,uBAAuB,CAAC,MAAM,KAAK,gBAAgB,CAAC,MAAM,EAAE,CAAC;QACpE,qBAAqB;QACrB,mHAAmH;QACnH,wFAAwF;QACxF,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE,CAAC;IAEzD,CAAC;SAAM,CAAC;QACJ,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;IAC5C,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAAC,IAA4B,EAAE,OAAgB;IACvE,IAAI,OAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE,CAAC;QAC5B,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;SAAM,IAAI,IAAI,YAAY,UAAU,EAAE,CAAC;QACpC,iBAAiB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACrC,CAAC;SAAM,IAAI,IAAI,YAAY,sBAAsB,EAAE,CAAC;QAChD,oBAAoB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;SAAM,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;QACrC,kBAAkB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IACtC,CAAC;AACL,CAAC;AAED,SAAS,UAAU,CAAC,IAA4B,EAAE,GAAY;IAC1D,IAAI,OAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,sFAAsF;IACpH,CAAC;SAAM,IAAI,IAAI,YAAY,sBAAsB,EAAE,CAAC;QAChD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACvD,CAAC;SAAM,IAAI,IAAI,YAAY,WAAW,EAAE,CAAC;QACrC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,GAAG,CAAC,kBAAkB,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;IACrE,CAAC;SAAM,CAAC;QACJ,OAAO,KAAK,CAAC;IACjB,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAY,EAAE,OAAgB;IACrD,IAAI,IAAI,EAAE,CAAC;QACP,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YACxB,mBAAmB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACxC,CAAC;QACD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;AACL,CAAC;AAED,SAAS,mBAAmB,CAAC,GAAY,EAAE,SAAkB;;IACzD,IAAI,MAAM,GAAG,EAAE,CAAC;IAChB,KAAK,MAAM,UAAU,IAAI,GAAG,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QACzF,MAAM,IAAI,MAAA,UAAU,CAAC,WAAW,mCAAI,GAAG,CAAC,kBAAkB,CAAC;IAC/D,CAAC;IACD,GAAG,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;IACzB,GAAG,CAAC,aAAa,GAAG,KAAK,CAAC;AAC9B,CAAC;AAED,SAAS,oBAAoB,CAAC,IAA4B,EAAE,OAAgB;IACxE,IAAI,WAAW,GAAoC,SAAS,CAAC;IAE7D,MAAM,YAAY,GAA6B,eAAe,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAClF,IAAI,YAAY,EAAE,CAAC;QACf,WAAW,GAAG,OAAO,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;IACxD,CAAC;IAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;QAChC,mBAAmB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;IACxC,CAAC;IAED,IAAI,WAAW,EAAE,CAAC;QACd,OAAO,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;QAEpC,MAAM,cAAc,GAAG,OAAO,CAAC,2BAA2B,EAAE,CAAC;QAC7D,IAAI,cAAc,IAAI,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,OAAO,MAAK,cAAc,EAAE,CAAC;YAC7D,uHAAuH;YACvH,+DAA+D;YAC/D,OAAO,YAAY,CAAC,OAAO,CAAC;QAChC,CAAC;QAED,WAAW,CAAC,QAAQ,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAC1E,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CAAC,IAAgB,EAAE,OAAgB;;IACzD,IAAI,UAAU,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;YACnD,OAAO,CAAC,MAAM,CAAC,MAAA,IAAI,CAAC,WAAW,mCAAI,OAAO,CAAC,kBAAkB,EAAE,IAAI,CAAC,CAAC;QACzE,CAAC;QACD,IAAI,CAAC;YACD,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAC7B,oBAAoB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC;gBAAS,CAAC;YACP,OAAO,CAAC,cAAc,EAAE,CAAC;QAC7B,CAAC;IACL,CAAC;AACL,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAiB,EAAE,OAAgB;IAC3D,IAAI,IAAI,CAAC,UAAU,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE,CAAC;QACnE,OAAO,CAAC,gBAAgB,EAAE,CAAC;IAC/B,CAAC;SAAM,CAAC;QACJ,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;YACxB,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QACvC,CAAC;QACD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACnC,OAAO,CAAC,UAAU,EAAE,CAAC;IACzB,CAAC;AACL,CAAC;AAED,SAAS,gBAAgB,CAAC,IAAY;IAClC,OAAO,IAAI,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC;AACnC,CAAC"}