{"version": 3, "file": "grammar-scope.js", "sourceRoot": "", "sources": ["../../../src/grammar/references/grammar-scope.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAShF,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAClE,OAAO,EAAE,uBAAuB,EAAE,MAAM,uCAAuC,CAAC;AAChF,OAAO,EAAE,oBAAoB,EAAE,MAAM,oCAAoC,CAAC;AAC1E,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,WAAW,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC5G,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,IAAI,EAAE,MAAM,kCAAkC,CAAC;AAChJ,OAAO,EAAE,gBAAgB,EAAE,MAAM,6BAA6B,CAAC;AAE/D,MAAM,OAAO,2BAA4B,SAAQ,oBAAoB;IAIjE,YAAY,QAA6B;QACrC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;IACvE,CAAC;IAEQ,QAAQ,CAAC,OAAsB;QACpC,MAAM,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAChE,IAAI,aAAa,KAAK,YAAY,EAAE,CAAC;YACjC,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;aAAM,CAAC;YACJ,OAAO,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACnC,CAAC;IACL,CAAC;IAEO,YAAY,CAAC,aAAqB,EAAE,OAAsB;QAC9D,IAAI,UAAkD,CAAC;QACvD,MAAM,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,iBAAiB,CAAC;QACrE,MAAM,QAAQ,GAAG,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACjD,IAAI,WAAW,IAAI,QAAQ,EAAE,CAAC;YAC1B,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;YAClD,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC7B,UAAU,GAAG,MAAM,CAAC,eAAe,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC;YACjI,CAAC;QACL,CAAC;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;QAChE,IAAI,UAAU,EAAE,CAAC;YACb,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;QACrD,CAAC;aAAM,CAAC;YACJ,OAAO,WAAW,CAAC;QACvB,CAAC;IACL,CAAC;IAEkB,cAAc,CAAC,aAAqB,EAAE,OAAsB;QAC3E,MAAM,OAAO,GAAG,kBAAkB,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACjE,IAAI,CAAC,OAAO,EAAE,CAAC;YACX,OAAO,WAAW,CAAC;QACvB,CAAC;QACD,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAC1C,IAAI,gBAAgB,GAAG,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,aAAa,EAAE,YAAY,CAAC,CAAC;QAClF,IAAI,aAAa,KAAK,YAAY,EAAE,CAAC;YACjC,gBAAgB,GAAG,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,SAAS,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC;QAChI,CAAC;QACD,OAAO,IAAI,QAAQ,CAAC,gBAAgB,CAAC,CAAC;IAC1C,CAAC;IAEO,aAAa,CAAC,OAAgB,EAAE,YAAyB;QAC7D,KAAK,MAAM,MAAM,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;YACnC,MAAM,GAAG,GAAG,gBAAgB,CAAC,MAAM,CAAC,CAAC;YACrC,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,EAAE,CAAC;gBAC3C,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,CAAC;gBACjC,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;gBAChE,IAAI,gBAAgB,EAAE,CAAC;oBACnB,MAAM,QAAQ,GAAG,gBAAgB,CAAC,WAAW,CAAC,KAAK,CAAC;oBACpD,IAAI,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;wBACtB,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;oBAC/C,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;CAEJ;AAED,MAAM,OAAO,8BAA+B,SAAQ,uBAAuB;IAGvE,YAAY,QAA6B;QACrC,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,IAAI,CAAC,cAAc,GAAG,QAAQ,CAAC,SAAS,CAAC,cAAc,CAAC;IAC5D,CAAC;IAEkB,UAAU,CAAC,IAAa,EAAE,OAA6B,EAAE,QAAyB;QACjG,uEAAuE;;QAEvE;;;;;;;;;;WAUG;QAEH,iFAAiF;QACjF,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC;QAE1C,uCAAuC;QACvC,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACrC,kEAAkE;gBAClE,MAAM,QAAQ,GAAG,MAAA,IAAI,CAAC,YAAY,mCAAI,IAAI,CAAC;gBAC3C,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;YACxF,CAAC;YACD,iBAAiB,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACxC,IAAI,QAAQ,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,YAAY,EAAE,CAAC;oBAChD,8CAA8C;oBAC9C,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,SAAS,CAAC,YAAY,EAAE,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;gBACpH,CAAC;YACL,CAAC,CAAC,CAAC;QACP,CAAC;IACL,CAAC;IAEkB,WAAW,CAAC,IAAa,EAAE,QAAyB,EAAE,MAAyB;QAC9F,0CAA0C;QAC1C,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;YACrB,OAAO;QACX,CAAC;QACD,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC7C,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;QAC/C,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACO,eAAe,CAAC,IAAa,EAAE,QAAyB,EAAE,MAAyB;;QACzF,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;QAClC,IAAI,SAAS,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACxE,MAAM,QAAQ,GAAG,MAAA,IAAI,CAAC,YAAY,mCAAI,IAAI,CAAC;YAC3C,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,6BAA6B,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;QACjG,CAAC;IACL,CAAC;IAED;;;;OAIG;IACO,iBAAiB,CAAC,IAAa,EAAE,QAAyB,EAAE,MAAyB;QAC3F,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,SAAS,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;YACnD,MAAM,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;QACnH,CAAC;IACL,CAAC;IAES,6BAA6B,CAAC,IAAa,EAAE,IAAY,EAAE,WAA4B,WAAW,CAAC,IAAI,CAAC;QAC9G,IAAI,eAA4C,CAAC;QACjD,MAAM,iBAAiB,GAAG,GAAG,EAAE,kBAAC,eAAe,aAAf,eAAe,cAAf,eAAe,IAAf,eAAe,GAAK,iBAAiB,CAAC,MAAA,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,mCAAI,IAAI,CAAC,QAAQ,CAAC,IAAA,CAAC;QAC5H,OAAO;YACH,IAAI;YACJ,IAAI;YACJ,IAAI,WAAW;gBACX,OAAO,iBAAiB,EAAE,CAAC;YAC/B,CAAC;YACD,gBAAgB,EAAE,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC;YAClD,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,QAAQ,CAAC,GAAG;YACzB,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC;SACjD,CAAC;IACN,CAAC;CACJ"}