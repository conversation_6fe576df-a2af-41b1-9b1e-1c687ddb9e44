{"version": 3, "file": "ast-collector.js", "sourceRoot": "", "sources": ["../../../src/grammar/type-system/ast-collector.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAOhF,OAAO,EAAE,YAAY,EAAE,2BAA2B,EAAE,MAAM,iBAAiB,CAAC;AAC5E,OAAO,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AACtI,OAAO,EAAE,oBAAoB,EAAE,MAAM,+BAA+B,CAAC;AACrE,OAAO,EAAE,YAAY,EAAE,MAAM,iCAAiC,CAAC;AAE/D;;;;;GAKG;AACH,MAAM,UAAU,UAAU,CAAC,QAA6B,EAAE,SAA4B;IAClF,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,oBAAoB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IACzE,OAAO,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC9C,CAAC;AAED;;;;;;GAMG;AACH,MAAM,UAAU,oBAAoB,CAAC,QAA6B,EAAE,SAA4B;IAC5F,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,oBAAoB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IAEvF,OAAO;QACH,YAAY;QACZ,QAAQ,EAAE,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC;QAC5C,QAAQ,EAAE,cAAc,CAAC,QAAQ,EAAE,QAAQ,CAAC;KAC/C,CAAC;AACN,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,KAAoB,EAAE,MAAsB;;IACvE,MAAM,QAAQ,GAAkB;QAC5B,UAAU,EAAE,2BAA2B,CAAC,wBAAwB,CAAiB,GAAG,KAAK,CAAC,UAAU,EAAE,GAAG,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,UAAU,mCAAI,EAAE,CAAC,CAAC;QACnI,MAAM,EAAE,wBAAwB,CAAa,GAAG,KAAK,CAAC,MAAM,EAAE,GAAG,MAAA,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,MAAM,mCAAI,EAAE,CAAC;KACzF,CAAC;IAEF,MAAM,UAAU,GAAG,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC1C,wBAAwB,CAAC,UAAU,CAAC,CAAC;IACrC,OAAO,UAAU,CAAC;AACtB,CAAC;AAED;;;;GAIG;AACH,SAAS,wBAAwB,CAA6B,GAAG,QAAa;IAC1E,OAAO,KAAK,CAAC,IAAI,CAAC,QAAQ;SACrB,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,EAAa,CAAC;SACtF,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAChE,CAAC;AAED,MAAM,UAAU,wBAAwB,CAAC,QAAkB;IACvD,MAAM,UAAU,GAAG,wBAAwB,CAAC,QAAQ,CAAC,CAAC;IACtD,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,WAAW,CAAC,KAAK,CAAC,CAAC;IACnB,mBAAmB,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;IACzC,cAAc,CAAC,KAAK,CAAC,CAAC;AAC1B,CAAC;AAED,SAAS,cAAc,CAAC,KAAmB;IACvC,wCAAwC;IACxC,MAAM,OAAO,GAAG,IAAI,GAAG,EAAc,CAAC;IACtC,MAAM,OAAO,GAAG,CAAC,IAAgB,EAAQ,EAAE;QACvC,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;YAAE,OAAO;QAC9B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClB,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC9B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,OAAO,CAAC,OAAO,CAAC,CAAC;YACjB,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1D,CAAC;IACL,CAAC,CAAC;IACF,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC3B,CAAC;AAED;;;GAGG;AACH,SAAS,wBAAwB,CAAC,EAAE,UAAU,EAAE,MAAM,EAAY;IAC9D,MAAM,UAAU,GAAI,UAA2B,CAAC,MAAM,CAAC,MAAM,CAAC;SACzD,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,IAAI,GAAG,EAAsB,CAAC,CAAC;IAE5F,MAAM,KAAK,GAAG,IAAI,GAAG,EAAsB,CAAC;IAE5C,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;QACzB,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;IACxD,CAAC;IACD,KAAK,MAAM,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,KAAK,EAAE,CAAC;QACtC,IAAI,UAAU,EAAE,CAAC;YACb,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAClC,CAAC;IACL,CAAC;IACD,OAAO,UAAU,CAAC;AACtB,CAAC;AAED,SAAS,UAAU,CAAC,QAAsB,EAAE,OAA0B;IAClE,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC;IAChB,CAAC;IACD,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACtB,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC5B,OAAO,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;IAC7D,CAAC;SAAM,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC/B,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC;QAC7B,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;YACrB,OAAO,UAAU,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC3C,CAAC;aAAM,CAAC;YACJ,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,OAAO,eAAe,CAAC,QAAQ,CAAC,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;IAC/D,CAAC;AACL,CAAC;AAED,SAAS,WAAW,CAAC,KAAmB;IACpC,KAAK,MAAM,aAAa,IAAI,KAAK,EAAE,CAAC;QAChC,KAAK,MAAM,aAAa,IAAI,aAAa,CAAC,UAAU,EAAE,CAAC;YACnD,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QAC9C,CAAC;IACL,CAAC;AACL,CAAC;AAED;;;GAGG;AACH,SAAS,mBAAmB,CAAC,UAA2B;;IACpD,MAAM,eAAe,GAAG,UAAU;SAC7B,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAE,OAAO,GAAG,CAAC,CAAC,CAAC,EAC7D,IAAI,GAAG,EAAyB,CACnC,CAAC;IAEN,qCAAqC;IAErC,KAAK,MAAM,aAAa,IAAI,UAAU,EAAE,CAAC;QACrC,MAAM,KAAK,GAAG,aAAa,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;QACxF,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACvB,MAAA,eAAe,CAAC,GAAG,CAAC,IAAI,CAAC,0CAAE,cAAc,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;QACjE,CAAC;IACL,CAAC;IAED,+DAA+D;IAE/D,uFAAuF;IACvF,yFAAyF;IACzF,MAAM,mBAAmB,GAAgB,IAAI,GAAG,EAAE,CAAC;IAEnD,MAAM,KAAK,GAAoB,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;IACvF,MAAM,OAAO,GAAuB,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;IAEnD,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtB,MAAM,MAAM,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,MAAM,EAAE,CAAC;YACT,KAAK,MAAM,SAAS,IAAI,MAAM,CAAC,UAAU,EAAE,CAAC;gBACxC,IAAI,eAAe,CAAC,SAAS,CAAC,EAAE,CAAC;oBAC7B,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;wBACnC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;wBACxC,SAAS,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;oBACrC,CAAC;yBAAM,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;wBAClD,MAAM,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;oBACxE,CAAC;oBAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;wBAC1B,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;wBACvB,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC1B,CAAC;gBACL,CAAC;YACL,CAAC;QACL,CAAC;IACL,CAAC;AACL,CAAC"}