{"version": 3, "file": "all-types.js", "sourceRoot": "", "sources": ["../../../../src/grammar/type-system/type-collector/all-types.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAOhF,OAAO,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EAAE,oBAAoB,EAAE,MAAM,qBAAqB,CAAC;AAC3D,OAAO,EAAE,WAAW,EAAE,MAAM,6BAA6B,CAAC;AAC1D,OAAO,EAAE,YAAY,EAAE,MAAM,qCAAqC,CAAC;AACnE,OAAO,EAAE,aAAa,EAAE,MAAM,gCAAgC,CAAC;AAC/D,OAAO,EAAE,cAAc,EAAE,MAAM,iCAAiC,CAAC;AAqBjE,MAAM,UAAU,oBAAoB,CAAC,QAA6B,EAAE,SAA4B;IAC5F,MAAM,YAAY,GAAG,sBAAsB,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC;IACjE,MAAM,QAAQ,GAAG,oBAAoB,CAAC,YAAY,CAAC,UAAU,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC;IACnF,MAAM,QAAQ,GAAG,oBAAoB,CAAC,YAAY,CAAC,WAAW,EAAE,YAAY,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;IAEtG,OAAO;QACH,YAAY;QACZ,QAAQ;QACR,QAAQ;KACX,CAAC;AACN,CAAC;AAED,+EAA+E;AAE/E,MAAM,UAAU,sBAAsB,CAAC,QAA6B,EAAE,SAA4B,EAAE,UAAoB,IAAI,GAAG,EAAE,EAC7H,eAA6B,EAAE,WAAW,EAAE,EAAE,EAAE,aAAa,EAAE,EAAE,EAAE,UAAU,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE;IAE9F,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;QAAE,QAAQ,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpD,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC7B,MAAM,GAAG,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;QACjC,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YACvB,SAAS;QACb,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACrB,KAAK,MAAM,IAAI,IAAI,OAAO,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACvC,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;oBACvB,YAAY,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC1C,CAAC;qBAAM,CAAC;oBACJ,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACxC,CAAC;YACL,CAAC;QACL,CAAC;QACD,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QAEvD,IAAI,SAAS,EAAE,CAAC;YACZ,MAAM,gBAAgB,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAgB,EAAE,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC;YAC5H,sBAAsB,CAAC,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAC/E,CAAC;IACL,CAAC;IACD,OAAO,YAAY,CAAC;AACxB,CAAC"}