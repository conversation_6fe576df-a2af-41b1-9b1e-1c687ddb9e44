{"version": 3, "file": "types-util.js", "sourceRoot": "", "sources": ["../../../src/grammar/type-system/types-util.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAQhF,OAAO,EAAE,QAAQ,EAAE,MAAM,4BAA4B,CAAC;AACtD,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,kCAAkC,CAAC;AAClG,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe,EAAE,WAAW,EAAE,MAAM,2BAA2B,CAAC;AAExH;;;GAGG;AACH,MAAM,UAAU,yBAAyB,CAAC,UAA4B;IAClE,MAAM,GAAG,GAAG,IAAI,QAAQ,EAAyB,CAAC;IAClD,KAAK,MAAM,aAAa,IAAI,UAAU,EAAE,CAAC;QACrC,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,UAAU,CAAC,CAAC;IAC7D,CAAC;IACD,KAAK,MAAM,aAAa,IAAI,UAAU,EAAE,CAAC;QACrC,KAAK,MAAM,SAAS,IAAI,aAAa,CAAC,UAAU,EAAE,CAAC;YAC/C,MAAM,mBAAmB,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC/C,IAAI,mBAAmB,EAAE,CAAC;gBACtB,GAAG,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,mBAAmB,CAAC,CAAC;YACxD,CAAC;QACL,CAAC;IACL,CAAC;IACD,OAAO,GAAG,CAAC;AACf,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAI,IAAS,EAAE,SAAkC;IAC9E,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AACrD,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,aAAwB,EAAE,UAAsB,EAAE,gBAAkC,EAAE,WAA2B;IAClJ,MAAM,aAAa,GAAG,IAAI,GAAG,EAAoB,CAAC;IAClD,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;IACjC,MAAM,IAAI,GAAG,UAAU,CAAC,cAAc,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;IAC1D,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;QACrB,MAAM,GAAG,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACxD,IAAI,CAAC,GAAG,EAAE,CAAC;YACP,SAAS;QACb,CAAC;QACD,MAAM,OAAO,GAAG,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;QAC9E,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;YACvB,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAC3B,MAAM,mBAAmB,GAAG,oBAAoB,CAAC,OAAO,EAAE,UAAU,EAAE,gBAAgB,EAAE,WAAW,CAAC,CAAC;YACrG,mBAAmB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;QACnE,CAAC;aAAM,IAAI,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAC/C,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAC1C,CAAC;IACL,CAAC;IACD,OAAO,aAAa,CAAC;AACzB,CAAC;AAED,MAAM,UAAU,oBAAoB,CAAC,KAAmB;IAIpD,MAAM,QAAQ,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;IAChC,MAAM,mBAAmB,GAAG,IAAI,QAAQ,EAAkB,CAAC;IAC3D,MAAM,iBAAiB,GAAG,IAAI,QAAQ,EAAkB,CAAC;IACzD,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;QAC1B,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YACtC,IAAI,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC1B,mBAAmB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;gBACnD,iBAAiB,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YACrD,CAAC;QACL,CAAC;QACD,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClC,IAAI,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC;gBACxB,mBAAmB,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;gBACjD,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YACnD,CAAC;QACL,CAAC;IACL,CAAC;IACD,MAAM,UAAU,GAAG,IAAI,QAAQ,EAAkB,CAAC;IAClD,MAAM,QAAQ,GAAG,IAAI,QAAQ,EAAkB,CAAC;IAChD,uBAAuB;IACvB,KAAK,MAAM,CAAC,IAAI,EAAE,aAAa,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAC/I,UAAU,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC;IAChE,CAAC;IACD,KAAK,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;QAC3I,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IACD,OAAO;QACH,UAAU;QACV,QAAQ;KACX,CAAC;AACN,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,QAAsB;IACpD,MAAM,UAAU,GAAG,IAAI,GAAG,EAAa,CAAC;IACxC,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;QACxB,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACzB,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;YACpC,IAAI,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBAC9B,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBAC7D,KAAK,MAAM,SAAS,IAAI,mBAAmB,EAAE,CAAC;oBAC1C,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;gBAC9B,CAAC;YACL,CAAC;QACL,CAAC,CAAC,CAAC;IACP,CAAC;SAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC1B,MAAM,SAAS,GAAG,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAClD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;YAC/B,MAAM,mBAAmB,GAAG,iBAAiB,CAAC,QAAQ,CAAC,CAAC;YACxD,KAAK,MAAM,SAAS,IAAI,mBAAmB,EAAE,CAAC;gBAC1C,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YAC9B,CAAC;QACL,CAAC;IACL,CAAC;IACD,OAAO,UAAU,CAAC;AACtB,CAAC;AAED,SAAS,gBAAgB,CAAC,cAA8B;;IACpD,IAAI,WAAW,CAAC,cAAc,CAAC,EAAE,CAAC;QAC9B,OAAO,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;SAAM,IAAI,YAAY,CAAC,cAAc,CAAC,EAAE,CAAC;QACtC,MAAM,KAAK,GAAG,MAAA,cAAc,CAAC,OAAO,0CAAE,GAAG,CAAC;QAC1C,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;YACtC,OAAO,CAAC,KAAK,CAAC,CAAC;QACnB,CAAC;IACL,CAAC;IACD,OAAO,EAAE,CAAC;AACd,CAAC;AAED,MAAM,UAAU,eAAe,CAAC,QAAkB,EAAE,QAAkB;IAClE,OAAO,QAAQ,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;AAC3D,CAAC;AAED,MAAM,UAAU,uBAAuB,CAAC,QAAkB;IACtD,OAAQ,QAAQ,CAAC,UAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;AACzE,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,2BAA2B,CAAC,UAA4B;IAMpE,MAAM,KAAK,GAAe,UAAU;SAC/B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;SAC5C,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAU,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAA,CAAC,CAAC;IACjD,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;IAC5E,CAAC;IACD,MAAM,CAAC,GAAe,EAAE,CAAC;IACzB,MAAM,CAAC,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;IAClD,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAClB,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAG,CAAC;QACrB,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC;YACjB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACV,KAAK;iBACA,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;iBAChC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC;IACL,CAAC;IACD,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;AAC/B,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,IAAkB;IAC3C,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,CAAC;SAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC;IAChB,CAAC;SAAM,CAAC;QACJ,OAAO,KAAK,CAAC;IACjB,CAAC;AACL,CAAC;AAED,MAAM,UAAU,cAAc,CAAC,IAAkB;IAC7C,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IACnD,CAAC;SAAM,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC,SAAS,KAAK,SAAS,CAAC;IACxC,CAAC;SAAM,CAAC;QACJ,OAAO,KAAK,CAAC;IACjB,CAAC;AACL,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,IAAkB;IACjD,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1D,CAAC;SAAM,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QAC/B,MAAM,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC;QACnC,IAAI,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC;YACvB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC;IACL,CAAC;SAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACxE,CAAC;IACD,OAAO,EAAE,CAAC;AACd,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,IAAkB;IAC3C,OAAO,oBAAoB,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AACjD,CAAC;AAED,SAAS,oBAAoB,CAAC,IAAkB,EAAE,OAA0B;IACxE,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QACpB,OAAO,EAAE,CAAC;IACd,CAAC;SAAM,CAAC;QACJ,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACtB,CAAC;IACD,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,oBAAoB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;IACrE,CAAC;SAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC;YAClB,OAAO,oBAAoB,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACrD,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC;IACL,CAAC;SAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACnF,CAAC;IACD,OAAO,EAAE,CAAC;AACd,CAAC;AAED,MAAM,UAAU,SAAS,CAAC,IAAkB;IACxC,OAAO,iBAAiB,CAAC,IAAI,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;AAC9C,CAAC;AAED,MAAM,UAAU,iBAAiB,CAAC,IAAkB,EAAE,OAAmC;IACrF,IAAI,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QACpB,OAAO,OAAO,CAAC,GAAG,CAAC,IAAI,CAAE,CAAC;IAC9B,CAAC;IACD,kDAAkD;IAClD,oDAAoD;IACpD,yEAAyE;IACzE,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAExB,IAAI,MAAM,GAAG,KAAK,CAAC;IAEnB,IAAI,eAAe,CAAC,IAAI,CAAC,EAAE,CAAC;QACxB,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;IAClE,CAAC;SAAM,IAAI,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QAC3B,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,MAAM,IAAI,KAAK,EAAE,CAAC;YAClB,MAAM,GAAG,iBAAiB,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QACpD,CAAC;aAAM,CAAC;YACJ,kCAAkC;YAClC,MAAM,GAAG,IAAI,CAAC;QAClB,CAAC;IACL,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;IAC1B,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,MAAM,UAAU,YAAY,CAAC,GAAW,EAAE,OAAkB,GAAG;IAC3D,IAAI,IAAI,KAAK,GAAG,EAAE,CAAC;QACf,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACpC,CAAC;SAAM,CAAC;QACJ,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IACpC,CAAC;AACL,CAAC"}