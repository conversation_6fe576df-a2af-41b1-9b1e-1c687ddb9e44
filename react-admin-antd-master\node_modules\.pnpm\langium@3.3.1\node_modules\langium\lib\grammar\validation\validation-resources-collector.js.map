{"version": 3, "file": "validation-resources-collector.js", "sourceRoot": "", "sources": ["../../../src/grammar/validation/validation-resources-collector.ts"], "names": [], "mappings": "AAAA;;;;gFAIgF;AAQhF,OAAO,EAAE,QAAQ,EAAE,MAAM,4BAA4B,CAAC;AACtD,OAAO,EAAE,MAAM,EAAE,MAAM,uBAAuB,CAAC;AAC/C,OAAO,EAAE,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAC;AACvG,OAAO,EAAE,eAAe,EAAE,uBAAuB,EAAE,MAAM,8BAA8B,CAAC;AACxF,OAAO,EAAE,oBAAoB,EAAE,MAAM,iCAAiC,CAAC;AACvE,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,8BAA8B,CAAC;AAE9E,MAAM,OAAO,0CAA0C;IAGnD,YAAY,QAAgC;QACxC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;IAChE,CAAC;IAED,0BAA0B,CAAC,OAAgB;QACvC,IAAI,CAAC;YACD,MAAM,aAAa,GAAG,oBAAoB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;YACpE,OAAO;gBACH,oBAAoB,EAAE,IAAI,CAAC,qBAAqB,CAAC,aAAa,CAAC;gBAC/D,qBAAqB,EAAE,IAAI,CAAC,sBAAsB,CAAC,aAAa,CAAC;aACpE,CAAC;QACN,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACX,OAAO,CAAC,KAAK,CAAC,uCAAuC,EAAE,GAAG,CAAC,CAAC;YAC5D,OAAO,EAAE,oBAAoB,EAAE,IAAI,GAAG,EAAE,EAAE,qBAAqB,EAAE,IAAI,GAAG,EAAE,EAAE,CAAC;QACjF,CAAC;IACL,CAAC;IAEO,qBAAqB,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,EAAsB;QAClF,MAAM,GAAG,GAAyB,IAAI,GAAG,EAAE,CAAC;QAC5C,MAAM,sBAAsB,GAAG,yBAAyB,CAAC,YAAY,CAAC,CAAC;QAEvE,KAAK,MAAM,IAAI,IAAI,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnD,GAAG,CAAC,GAAG,CACH,IAAI,CAAC,IAAI,EACT,EAAE,QAAQ,EAAE,IAAI,EAAE,aAAa,EAAE,sBAAsB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAC3E,CAAC;QACN,CAAC;QAED,MAAM,0BAA0B,GAAG,MAAM,CAAC,YAAY,CAAC,UAAU,CAAC;aAC7D,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC;aAC1B,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAC3C,IAAI,GAAG,EAA4B,CACtC,CAAC;QACN,KAAK,MAAM,IAAI,IAAI,uBAAuB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACnD,MAAM,IAAI,GAAG,0BAA0B,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvD,IAAI,IAAI,EAAE,CAAC;gBACP,MAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpC,GAAG,CAAC,GAAG,CACH,IAAI,CAAC,IAAI,kCACJ,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,EAAE,KAAE,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,IAC1D,CAAC;YACN,CAAC;QACL,CAAC;QAED,OAAO,GAAG,CAAC;IACf,CAAC;IAEO,sBAAsB,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAAsB;QACrE,MAAM,qBAAqB,GAA4B,IAAI,GAAG,EAAE,CAAC;QACjE,MAAM,UAAU,GAAG,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;QACvD,MAAM,YAAY,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/D,KAAK,MAAM,IAAI,IAAI,eAAe,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE,CAAC;YACrD,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC,CAAC;QACjG,CAAC;QACD,OAAO,qBAAqB,CAAC;IACjC,CAAC;IAEO,kBAAkB,CAAC,aAA4B,EAAE,GAA+B,EAAE,OAAoB;QAC1G,IAAI,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,OAAO,EAAE,CAAC;QACd,CAAC;QACD,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAChC,MAAM,UAAU,GAAe,CAAC,GAAG,aAAa,CAAC,UAAU,CAAC,CAAC;QAC7D,KAAK,MAAM,SAAS,IAAI,aAAa,CAAC,UAAU,EAAE,CAAC;YAC/C,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACtC,IAAI,KAAK,EAAE,CAAC;gBACR,UAAU,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;YACrE,CAAC;QACL,CAAC;QACD,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ;AAED,SAAS,yBAAyB,CAAC,EAAE,WAAW,EAAE,aAAa,EAAgB;IAC3E,MAAM,GAAG,GAAG,IAAI,QAAQ,EAA+B,CAAC;IAExD,gBAAgB;IAChB,MAAM,CAAC,WAAW,CAAC;SACd,MAAM,CAAC,aAAa,CAAC;SACrB,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;IAE3D,kBAAkB;IAClB,SAAS,cAAc,CAAC,OAAwB;QAC5C,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACpB,MAAM,IAAI,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;YACpC,IAAI,IAAI,EAAE,CAAC;gBACP,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;YAC3B,CAAC;QACL,CAAC;QAAC,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC,OAAO,CAAC,IAAI,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;YAC7E,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;QACrD,CAAC;IACL,CAAC;IAED,WAAW;SACN,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IAEtD,OAAO,GAAG,CAAC;AACf,CAAC"}