@import './styles/theme.css';
@import './styles/antd-override.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 基础样式 */
body {
  /* background-color: var(--bg-primary); */
  /* color: var(--text-primary); */
  overflow: hidden;
}

/* 通用过渡效果 */
* {
  transition-property: color, background-color, border-color;
  /* transition-duration: 200ms; */
}

/* 聊天列表项样式 - 使用更强的选择器 */
.ant-list .ant-list-item.chat-list-item {
  transition: all 0.2s ease !important;
  border-left: 3px solid transparent !important;
  border-radius: 0 !important;
  background-color: transparent !important;
}

.ant-list .ant-list-item.chat-list-item:hover {
  background-color: #f5f5f5 !important;
  transform: translateX(2px) !important;
}

.ant-list .ant-list-item.chat-list-item.active {
  background-color: #e6f7ff !important;
  border-left: 3px solid #1890ff !important;
  border-radius: 0 8px 8px 0 !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1) !important;
  margin: 2px 0 !important;
}

.ant-list .ant-list-item.chat-list-item.active:hover {
  background-color: #e6f7ff !important;
  transform: translateX(0) !important;
}

/* 备用样式 - 如果上面的不生效 */
[class*="chat-list-item"][class*="active"] {
  background-color: #e6f7ff !important;
  border-left: 3px solid #1890ff !important;
  border-radius: 0 8px 8px 0 !important;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1) !important;
  margin: 2px 0 !important;
}