import React, { useState } from "react";
import { Card, Row, Col, Avatar, Typography } from "antd";
import {
  AppstoreOutlined,
  RadarChartOutlined,
  LoginOutlined,
  ThunderboltOutlined,
  EyeInvisibleOutlined,
} from "@ant-design/icons";

import styles from "./index.module.scss";
const { Text, Link } = Typography;
const url =
  "https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg";
const categories = [
  { key: "全部", label: "全部", icon: <AppstoreOutlined /> },
  { key: "侦察", label: "侦察", icon: <RadarChartOutlined /> },
  { key: "初始访问", label: "初始访问", icon: <LoginOutlined /> },
  { key: "攻击执行", label: "攻击执行", icon: <ThunderboltOutlined /> },
  { key: "防御规避", label: "防御规避", icon: <EyeInvisibleOutlined /> },
];

const generateMockData = (type: string) => {
  return Array.from({ length: 8 }, (_, idx) => ({
    id: `${type}-${idx}`,
    title: `${type} 工具 ${idx + 1}`,
    description: `这是${type}类别的工具描述这是${type}类`,
    url: url,
    times: Math.floor(Math.random() * 1000) + 1,
  }));
};

const Hall: React.FC = () => {
  const [selected, setSelected] = useState("全部");
  const data = generateMockData(selected);

  // 处理卡片点击事件，在新标签页打开chat页面并携带卡片信息
  const handleCardClick = (item: any) => {
    // 将卡片信息编码为URL参数
    const cardInfo = {
      id: item.id,
      title: item.title,
      description: item.description,
      times: item.times,
      category: selected,
    };

    // 将对象转换为URL参数字符串
    const params = new URLSearchParams({
      cardInfo: JSON.stringify(cardInfo),
    }).toString();

    // 构建完整的URL（包含hash路由）
    const chatUrl = `${window.location.origin}${window.location.pathname}#/chat/index?${params}`;

    // 在新标签页中打开
    window.open(chatUrl, "_blank");
  };

  return (
    <div className={styles.container}>
      {/* 顶部图标+文字 标签栏 */}
      <div className={styles.tabBar}>
        {categories.map((cat) => (
          <div
            key={cat.key}
            className={`${styles.tabItem} ${
              selected === cat.key ? styles.active : ""
            }`}
            onClick={() => setSelected(cat.key)}
          >
            <div className={styles.icon}>{cat.icon}</div>
            <div className={styles.label}>{cat.label}</div>
          </div>
        ))}
      </div>

      {/* 内容区：antd 卡片 */}
      <Row gutter={[16, 16]}>
        {data.map((item) => (
          <Col key={item.id} xs={24} sm={12} md={8} lg={6}>
            <Card
              hoverable
              className={styles.hallCard}
              onClick={() => handleCardClick(item)}
              style={{ cursor: "pointer" }}
            >
              <div className={styles.cardHeader}>
                <Avatar src={url}></Avatar>
                <p className={styles.agent_name}>{item.title}</p>
              </div>
              <Text type="secondary">{item.description}</Text>

              <div>
                <Text type="secondary">{item.times}</Text>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default Hall;

export const routeConfig = {
  title: "route.agentsquare.agenthall",
  sort: 5,
};
