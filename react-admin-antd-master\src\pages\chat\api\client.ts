import { ChatMessage, RequestMessage } from '../types';

// 模拟AI回复的示例响应
const SAMPLE_RESPONSES = [
  // 简单回复
  "我理解您的问题。让我来帮您分析一下这个情况。",

  // 列表格式
  "这是一个很好的问题！根据我的理解，我可以为您提供以下建议：\n\n1. **首先**，我们需要考虑项目的整体架构\n2. **其次**，重要的是选择合适的技术栈\n3. **最后**，建议您制定详细的开发计划\n\n希望这些建议对您有帮助！",

  // 代码示例
  "我来为您提供一个React组件的示例：\n\n```jsx\nimport React, { useState } from 'react';\nimport { Button, Input, message } from 'antd';\n\nconst ChatComponent = () => {\n  const [input, setInput] = useState('');\n  \n  const handleSend = () => {\n    if (input.trim()) {\n      message.success('消息发送成功！');\n      setInput('');\n    }\n  };\n  \n  return (\n    <div>\n      <Input \n        value={input}\n        onChange={(e) => setInput(e.target.value)}\n        placeholder=\"输入消息...\"\n        onPressEnter={handleSend}\n      />\n      <Button type=\"primary\" onClick={handleSend}>\n        发送\n      </Button>\n    </div>\n  );\n};\n\nexport default ChatComponent;\n```\n\n这个组件展示了基本的聊天输入功能，包含了状态管理和事件处理。",

  // 表格数据
  "这里是一个技术对比表格：\n\n| 框架 | 优点 | 缺点 | 适用场景 |\n|------|------|------|----------|\n| React | 生态丰富、灵活性高 | 学习曲线陡峭 | 大型应用 |\n| Vue | 易学易用、文档完善 | 生态相对较小 | 中小型项目 |\n| Angular | 功能完整、企业级 | 复杂度高 | 企业应用 |\n\n每个框架都有其特点，选择时需要根据项目需求来决定。",

  // 引用块
  "关于最佳实践，我想分享一些重要观点：\n\n> **代码质量**\n> \n> 编写可维护的代码比快速完成功能更重要。好的代码应该是自文档化的，易于理解和修改。\n\n> **性能优化**\n> \n> 过早的优化是万恶之源，但这不意味着我们可以忽视性能。在设计阶段就要考虑性能影响。\n\n> **团队协作**\n> \n> 统一的代码规范和良好的沟通是团队成功的关键。",

  // 数学公式
  "让我用数学公式来解释这个算法的时间复杂度：\n\n对于快速排序算法：\n- 最好情况：$O(n \\log n)$\n- 平均情况：$O(n \\log n)$\n- 最坏情况：$O(n^2)$\n\n空间复杂度为 $O(\\log n)$（递归调用栈）。\n\n快速排序的核心思想是分治法：\n$$T(n) = 2T(n/2) + O(n)$$\n\n这个递推关系式说明了算法的效率。",

  // 多级标题
  "# React开发指南\n\n## 1. 环境搭建\n\n### 1.1 安装Node.js\n首先需要安装Node.js环境：\n```bash\n# 检查版本\nnode --version\nnpm --version\n```\n\n### 1.2 创建项目\n使用Create React App创建新项目：\n```bash\nnpx create-react-app my-app\ncd my-app\nnpm start\n```\n\n## 2. 核心概念\n\n### 2.1 组件\nReact应用由组件构成，组件是可复用的UI单元。\n\n### 2.2 状态管理\n使用`useState`和`useEffect`管理组件状态。\n\n## 3. 最佳实践\n\n- 保持组件简单和专一\n- 使用TypeScript提高代码质量\n- 编写单元测试\n- 遵循ESLint规则",

  // 混合格式
  "让我为您详细解答这个技术问题：\n\n## 🚀 解决方案\n\n### 方法一：使用Hooks\n\n```typescript\nimport { useState, useEffect, useCallback } from 'react';\n\ninterface User {\n  id: number;\n  name: string;\n  email: string;\n}\n\nconst useUserData = () => {\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(false);\n  \n  const fetchUsers = useCallback(async () => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/users');\n      const data = await response.json();\n      setUsers(data);\n    } catch (error) {\n      console.error('获取用户数据失败:', error);\n    } finally {\n      setLoading(false);\n    }\n  }, []);\n  \n  useEffect(() => {\n    fetchUsers();\n  }, [fetchUsers]);\n  \n  return { users, loading, refetch: fetchUsers };\n};\n```\n\n### 方法二：使用Context\n\n> **注意**：Context适用于跨组件共享状态，但不要过度使用。\n\n**优点：**\n- ✅ 避免prop drilling\n- ✅ 全局状态管理\n- ✅ 易于测试\n\n**缺点：**\n- ❌ 可能导致不必要的重渲染\n- ❌ 调试相对困难\n\n### 性能对比\n\n| 方案 | 性能 | 复杂度 | 推荐指数 |\n|------|------|--------|----------|\n| Hooks | ⭐⭐⭐⭐⭐ | 低 | ⭐⭐⭐⭐⭐ |\n| Context | ⭐⭐⭐⭐ | 中 | ⭐⭐⭐⭐ |\n| Redux | ⭐⭐⭐ | 高 | ⭐⭐⭐ |\n\n希望这个解答对您有帮助！如果还有疑问，请随时询问。"
];

// 模拟网络延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟流式响应
export async function* streamChatCompletion(
  messages: RequestMessage[],
  options?: {
    model?: string;
    temperature?: number;
    max_tokens?: number;
  }
): AsyncGenerator<string, void, unknown> {
  // 模拟思考延迟
  await delay(800 + Math.random() * 1200);

  // 选择一个随机回复
  const response = SAMPLE_RESPONSES[Math.floor(Math.random() * SAMPLE_RESPONSES.length)];

  // 按词汇分割，而不是字符，使流式效果更自然
  const segments = response.split(/(\s+|[，。！？；、\n])/);
  let currentText = '';

  for (let i = 0; i < segments.length; i++) {
    currentText += segments[i];
    yield currentText;

    // 根据内容类型调整延迟
    const segment = segments[i];
    let delayTime = 30; // 基础延迟

    if (segment.includes('\n')) {
      delayTime = 100; // 换行延迟更长
    } else if (/[，。！？；、]/.test(segment)) {
      delayTime = 200; // 标点符号延迟
    } else if (segment.includes('```')) {
      delayTime = 300; // 代码块延迟
    } else if (segment.trim().length > 0) {
      delayTime = 50 + Math.random() * 100; // 文字内容
    }

    await delay(delayTime);
  }
}

// 普通聊天完成API
export async function chatCompletion(
  messages: RequestMessage[],
  options?: {
    model?: string;
    temperature?: number;
    max_tokens?: number;
  }
): Promise<string> {
  // 模拟网络延迟
  await delay(1000 + Math.random() * 2000);
  
  // 选择一个随机回复
  const response = SAMPLE_RESPONSES[Math.floor(Math.random() * SAMPLE_RESPONSES.length)];
  
  return response;
}

// 检查模型是否可用
export async function checkModelAvailability(model: string): Promise<boolean> {
  // 模拟检查
  await delay(500);
  
  // 支持的模型列表
  const supportedModels = [
    'gpt-3.5-turbo',
    'gpt-4',
    'gpt-4o',
    'gpt-4o-mini',
    'claude-3-sonnet',
    'claude-3-haiku',
  ];
  
  return supportedModels.includes(model);
}

// 获取可用模型列表
export async function getAvailableModels(): Promise<Array<{
  id: string;
  name: string;
  description?: string;
}>> {
  await delay(300);
  
  return [
    {
      id: 'gpt-3.5-turbo',
      name: 'GPT-3.5 Turbo',
      description: '快速且经济的模型，适合大多数对话任务'
    },
    {
      id: 'gpt-4',
      name: 'GPT-4',
      description: '更强大的模型，适合复杂任务'
    },
    {
      id: 'gpt-4o',
      name: 'GPT-4o',
      description: '最新的多模态模型'
    },
    {
      id: 'gpt-4o-mini',
      name: 'GPT-4o Mini',
      description: '轻量级的GPT-4o模型'
    },
  ];
}

// 生成聊天标题
export async function generateTitle(messages: RequestMessage[]): Promise<string> {
  await delay(800);
  
  // 从第一条用户消息生成标题
  const firstUserMessage = messages.find(m => m.role === 'user');
  if (!firstUserMessage) {
    return '新的聊天';
  }
  
  const content = typeof firstUserMessage.content === 'string' 
    ? firstUserMessage.content 
    : firstUserMessage.content.filter(c => c.type === 'text').map(c => c.text).join('');
  
  // 简单的标题生成逻辑
  const keywords = content.split(/\s+/).slice(0, 3).join(' ');
  const titles = [
    `关于${keywords}的讨论`,
    `${keywords}相关问题`,
    `${keywords}咨询`,
    `讨论${keywords}`,
    keywords.length > 10 ? keywords.slice(0, 10) + '...' : keywords,
  ];
  
  return titles[Math.floor(Math.random() * titles.length)];
}

// 错误处理
export class ChatAPIError extends Error {
  constructor(
    message: string,
    public code?: string,
    public status?: number
  ) {
    super(message);
    this.name = 'ChatAPIError';
  }
}

// API配置
export interface APIConfig {
  baseURL?: string;
  apiKey?: string;
  timeout?: number;
  retries?: number;
}

// API客户端类
export class ChatAPIClient {
  private config: APIConfig;
  
  constructor(config: APIConfig = {}) {
    this.config = {
      timeout: 30000,
      retries: 3,
      ...config,
    };
  }
  
  async chat(messages: RequestMessage[], options?: any): Promise<string> {
    try {
      return await chatCompletion(messages, options);
    } catch (error) {
      throw new ChatAPIError(
        error instanceof Error ? error.message : '聊天请求失败',
        'CHAT_ERROR'
      );
    }
  }
  
  async *streamChat(messages: RequestMessage[], options?: any): AsyncGenerator<string, void, unknown> {
    try {
      yield* streamChatCompletion(messages, options);
    } catch (error) {
      throw new ChatAPIError(
        error instanceof Error ? error.message : '流式聊天请求失败',
        'STREAM_ERROR'
      );
    }
  }
  
  async getModels() {
    try {
      return await getAvailableModels();
    } catch (error) {
      throw new ChatAPIError(
        error instanceof Error ? error.message : '获取模型列表失败',
        'MODELS_ERROR'
      );
    }
  }
  
  async generateTitle(messages: RequestMessage[]): Promise<string> {
    try {
      return await generateTitle(messages);
    } catch (error) {
      console.warn('生成标题失败:', error);
      return '新的聊天';
    }
  }
}

// 默认客户端实例
export const defaultChatClient = new ChatAPIClient();
