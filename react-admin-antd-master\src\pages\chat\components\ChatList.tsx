import React, { useState } from "react";
import {
  List,
  Button,
  Input,
  Space,
  Typography,
  Dropdown,
  Modal,
  message,
  Empty,
} from "antd";
import {
  PlusOutlined,
  SearchOutlined,
  MoreOutlined,
  DeleteOutlined,
  EditOutlined,
  MessageOutlined,
} from "@ant-design/icons";
import { observer } from "mobx-react-lite";
import { useChatStoreOnly, useConfigStore } from "../stores";
import { ChatSession } from "../types";

const { Text } = Typography;
const { Search } = Input;

interface ChatListProps {
  onSelectSession?: (sessionIndex: number) => void;
  style?: React.CSSProperties;
}

export const ChatList: React.FC<ChatListProps> = observer(
  ({ onSelectSession, style }) => {
    const chatStore = useChatStoreOnly();
    const configStore = useConfigStore();
    const [searchKeyword, setSearchKeyword] = useState("");
    const [editingSessionId, setEditingSessionId] = useState<string | null>(
      null
    );
    const [editingTitle, setEditingTitle] = useState("");

    const filteredSessions = chatStore.sessions.filter(
      (session) =>
        session.topic.toLowerCase().includes(searchKeyword.toLowerCase()) ||
        session.messages.some((msg) =>
          typeof msg.content === "string"
            ? msg.content.toLowerCase().includes(searchKeyword.toLowerCase())
            : false
        )
    );

    const handleNewChat = () => {
      chatStore.newSession();
      message.success("新建聊天成功");
    };

    const handleCreateDemo = () => {
      chatStore.createDemoSession();
      message.success("演示会话已创建");
    };

    const handleSelectSession = (index: number) => {
      chatStore.selectSession(index);
      onSelectSession?.(index);
    };

    const handleDeleteSession = (index: number, e: React.MouseEvent) => {
      e.stopPropagation();

      Modal.confirm({
        title: "确认删除",
        content: "确定要删除这个聊天会话吗？此操作不可恢复。",
        okText: "删除",
        okType: "danger",
        cancelText: "取消",
        onOk: () => {
          try {
            chatStore.deleteSession(index);
            message.success("聊天会话已删除");

            // 强制触发重新渲染，确保列表更新
            setTimeout(() => {
              // 如果有回调，通知父组件会话已切换
              if (onSelectSession && chatStore.sessions.length > 0) {
                onSelectSession(chatStore.currentSessionIndex);
              }
            }, 100);
          } catch (error) {
            console.error("删除会话失败:", error);
            message.error("删除会话失败，请重试");
          }
        },
      });
    };

    const handleEditTitle = (session: ChatSession, e: React.MouseEvent) => {
      e.stopPropagation();
      setEditingSessionId(session.id);
      setEditingTitle(session.topic);
    };

    const handleSaveTitle = () => {
      if (editingSessionId && editingTitle.trim()) {
        const sessionIndex = chatStore.sessions.findIndex(
          (s) => s.id === editingSessionId
        );
        if (sessionIndex >= 0) {
          chatStore.updateMessage(sessionIndex, -1, () => {
            chatStore.sessions[sessionIndex].topic = editingTitle.trim();
          });
          message.success("标题已更新");
        }
      }
      setEditingSessionId(null);
      setEditingTitle("");
    };

    const handleCancelEdit = () => {
      setEditingSessionId(null);
      setEditingTitle("");
    };

    const getSessionMenuItems = (session: ChatSession, index: number) => [
      {
        key: "edit",
        label: "编辑标题",
        icon: <EditOutlined />,
        onClick: (e: any) => handleEditTitle(session, e.domEvent),
      },
      {
        key: "delete",
        label: "删除会话",
        icon: <DeleteOutlined />,
        danger: true,
        onClick: (e: any) => handleDeleteSession(index, e.domEvent),
      },
    ];

    const formatLastUpdate = (timestamp: number) => {
      const now = Date.now();
      const diff = now - timestamp;

      if (diff < 60000) return "刚刚";
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
      return `${Math.floor(diff / 86400000)}天前`;
    };

    const getLastMessage = (session: ChatSession) => {
      if (session.messages.length === 0) return "暂无消息";

      const lastMessage = session.messages[session.messages.length - 1];
      const content =
        typeof lastMessage.content === "string"
          ? lastMessage.content
          : lastMessage.content
              .filter((c) => c.type === "text")
              .map((c) => c.text)
              .join("");

      return content.slice(0, 50) + (content.length > 50 ? "..." : "");
    };

    return (
      <div
        style={{
          height: "100%",
          display: "flex",
          flexDirection: "column",
          ...style,
        }}
      >
        {/* 头部操作区 */}
        <div style={{ padding: "16px", borderBottom: "1px solid #f0f0f0" }}>
          <Space direction="vertical" style={{ width: "100%" }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleNewChat}
              block
            >
              新建聊天
            </Button>

            <Button
              type="dashed"
              onClick={handleCreateDemo}
              block
              style={{ fontSize: "12px" }}
            >
              📝 创建演示会话
            </Button>

            <Search
              placeholder="搜索聊天记录..."
              value={searchKeyword}
              onChange={(e) => setSearchKeyword(e.target.value)}
              allowClear
            />
          </Space>
        </div>

        {/* 聊天列表 */}
        <div style={{ flex: 1, overflow: "auto" }}>
          {filteredSessions.length === 0 ? (
            <Empty description="暂无聊天记录" style={{ marginTop: "60px" }} />
          ) : (
            <List
              dataSource={filteredSessions}
              renderItem={(session, index) => {
                const actualIndex = chatStore.sessions.findIndex(
                  (s) => s.id === session.id
                );
                const isActive = actualIndex === chatStore.currentSessionIndex;
                const isEditing = editingSessionId === session.id;

                return (
                  <List.Item
                    className={`chat-list-item ${isActive ? "active" : ""}`}
                    style={{
                      padding: "12px 16px",
                      cursor: "pointer",
                    }}
                    onClick={() =>
                      !isEditing && handleSelectSession(actualIndex)
                    }
                    actions={[
                      <div onClick={(e) => e.stopPropagation()}>
                        <Dropdown
                          key="more"
                          menu={{
                            items: getSessionMenuItems(session, actualIndex),
                          }}
                          trigger={["click"]}
                        >
                          <Button
                            type="text"
                            size="small"
                            icon={<MoreOutlined />}
                            style={{ opacity: 0.6 }}
                          />
                        </Dropdown>
                      </div>,
                    ]}
                  >
                    <List.Item.Meta
                      avatar={
                        <MessageOutlined
                          style={{ fontSize: "16px", color: "#1890ff" }}
                        />
                      }
                      title={
                        isEditing ? (
                          <Input
                            value={editingTitle}
                            onChange={(e) => setEditingTitle(e.target.value)}
                            onPressEnter={handleSaveTitle}
                            onBlur={handleSaveTitle}
                            onKeyDown={(e) => {
                              if (e.key === "Escape") {
                                handleCancelEdit();
                              }
                            }}
                            size="small"
                            autoFocus
                            onClick={(e) => e.stopPropagation()}
                          />
                        ) : (
                          <Text
                            strong={isActive}
                            style={{
                              fontSize: configStore.fontSize * 0.9,
                              color: isActive ? "#1890ff" : "#333",
                            }}
                          >
                            {session.topic}
                          </Text>
                        )
                      }
                      description={
                        <div>
                          <Text
                            type="secondary"
                            style={{
                              fontSize: configStore.fontSize * 0.8,
                              display: "block",
                              marginBottom: "4px",
                            }}
                          >
                            {getLastMessage(session)}
                          </Text>
                          <Text
                            type="secondary"
                            style={{ fontSize: configStore.fontSize * 0.7 }}
                          >
                            {formatLastUpdate(session.lastUpdate)} ·{" "}
                            {session.messages.length}条消息
                          </Text>
                        </div>
                      }
                    />
                  </List.Item>
                );
              }}
            />
          )}
        </div>
      </div>
    );
  }
);
