import React from 'react';
import { 
  Modal, 
  Form, 
  Input, 
  Select, 
  Slider, 
  Switch, 
  Button, 
  Space, 
  Divider,
  message,
  Tabs 
} from 'antd';
import { observer } from 'mobx-react-lite';
import { useConfigStore } from '../stores';
import { Theme, SubmitKey } from '../constants';

const { Option } = Select;
const { TextArea } = Input;

interface SettingsProps {
  visible: boolean;
  onClose: () => void;
}

export const Settings: React.FC<SettingsProps> = observer(({
  visible,
  onClose
}) => {
  const configStore = useConfigStore();
  const [form] = Form.useForm();

  const handleSave = () => {
    form.validateFields().then(values => {
      configStore.updateConfig(config => {
        Object.assign(config, values);
      });
      message.success('设置已保存');
      onClose();
    });
  };

  const handleReset = () => {
    Modal.confirm({
      title: '确认重置',
      content: '确定要重置所有设置为默认值吗？',
      okText: '重置',
      okType: 'danger',
      cancelText: '取消',
      onOk: () => {
        configStore.reset();
        form.resetFields();
        message.success('设置已重置');
      },
    });
  };

  const generalSettings = (
    <Form
      form={form}
      layout="vertical"
      initialValues={configStore.config}
      style={{ maxHeight: '400px', overflow: 'auto' }}
    >
      <Form.Item
        label="主题"
        name="theme"
        tooltip="选择应用主题"
      >
        <Select>
          <Option value={Theme.Auto}>跟随系统</Option>
          <Option value={Theme.Light}>浅色主题</Option>
          <Option value={Theme.Dark}>深色主题</Option>
        </Select>
      </Form.Item>

      <Form.Item
        label="字体大小"
        name="fontSize"
        tooltip="调整聊天界面的字体大小"
      >
        <Slider
          min={12}
          max={24}
          marks={{
            12: '12px',
            14: '14px',
            16: '16px',
            18: '18px',
            20: '20px',
            24: '24px'
          }}
        />
      </Form.Item>

      <Form.Item
        label="发送快捷键"
        name="submitKey"
        tooltip="选择发送消息的快捷键"
      >
        <Select>
          <Option value={SubmitKey.Enter}>Enter</Option>
          <Option value={SubmitKey.CtrlEnter}>Ctrl + Enter</Option>
          <Option value={SubmitKey.ShiftEnter}>Shift + Enter</Option>
        </Select>
      </Form.Item>

      <Form.Item
        label="自动生成标题"
        name="enableAutoGenerateTitle"
        valuePropName="checked"
        tooltip="根据对话内容自动生成聊天标题"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        label="发送预览气泡"
        name="sendPreviewBubble"
        valuePropName="checked"
        tooltip="发送消息前显示预览气泡"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        label="紧凑边框"
        name="tightBorder"
        valuePropName="checked"
        tooltip="使用更紧凑的界面布局"
      >
        <Switch />
      </Form.Item>
    </Form>
  );

  const modelSettings = (
    <Form
      layout="vertical"
      initialValues={configStore.config.modelConfig}
      style={{ maxHeight: '400px', overflow: 'auto' }}
    >
      <Form.Item
        label="默认模型"
        name="model"
        tooltip="选择默认使用的AI模型"
      >
        <Select>
          <Option value="gpt-3.5-turbo">GPT-3.5 Turbo</Option>
          <Option value="gpt-4">GPT-4</Option>
          <Option value="gpt-4o">GPT-4o</Option>
          <Option value="gpt-4o-mini">GPT-4o Mini</Option>
        </Select>
      </Form.Item>

      <Form.Item
        label="温度"
        name="temperature"
        tooltip="控制回复的随机性，值越高越随机"
      >
        <Slider
          min={0}
          max={2}
          step={0.1}
          marks={{
            0: '0',
            0.5: '0.5',
            1: '1',
            1.5: '1.5',
            2: '2'
          }}
        />
      </Form.Item>

      <Form.Item
        label="最大令牌数"
        name="max_tokens"
        tooltip="单次回复的最大令牌数"
      >
        <Slider
          min={100}
          max={8000}
          step={100}
          marks={{
            100: '100',
            2000: '2K',
            4000: '4K',
            8000: '8K'
          }}
        />
      </Form.Item>

      <Form.Item
        label="历史消息数量"
        name="historyMessageCount"
        tooltip="发送给AI的历史消息数量"
      >
        <Slider
          min={0}
          max={32}
          marks={{
            0: '0',
            4: '4',
            8: '8',
            16: '16',
            32: '32'
          }}
        />
      </Form.Item>

      <Form.Item
        label="发送记忆"
        name="sendMemory"
        valuePropName="checked"
        tooltip="是否发送对话记忆给AI"
      >
        <Switch />
      </Form.Item>
    </Form>
  );

  const advancedSettings = (
    <Form
      layout="vertical"
      initialValues={configStore.config}
      style={{ maxHeight: '400px', overflow: 'auto' }}
    >
      <Form.Item
        label="自定义模型"
        name="customModels"
        tooltip="添加自定义模型，用逗号分隔"
      >
        <TextArea
          rows={3}
          placeholder="例如: custom-model-1, custom-model-2"
        />
      </Form.Item>

      <Form.Item
        label="压缩消息长度阈值"
        name="compressMessageLengthThreshold"
        tooltip="当消息长度超过此值时进行压缩"
      >
        <Slider
          min={500}
          max={4000}
          step={100}
          marks={{
            500: '500',
            1000: '1K',
            2000: '2K',
            4000: '4K'
          }}
        />
      </Form.Item>

      <Form.Item
        label="禁用提示词提示"
        name="disablePromptHint"
        valuePropName="checked"
        tooltip="禁用输入时的提示词提示"
      >
        <Switch />
      </Form.Item>

      <Form.Item
        label="隐藏内置面具"
        name="hideBuiltinMasks"
        valuePropName="checked"
        tooltip="在面具列表中隐藏内置面具"
      >
        <Switch />
      </Form.Item>
    </Form>
  );

  const tabItems = [
    {
      key: 'general',
      label: '通用设置',
      children: generalSettings,
    },
    {
      key: 'model',
      label: '模型设置',
      children: modelSettings,
    },
    {
      key: 'advanced',
      label: '高级设置',
      children: advancedSettings,
    },
  ];

  return (
    <Modal
      title="设置"
      open={visible}
      onCancel={onClose}
      width={600}
      footer={
        <Space>
          <Button onClick={handleReset}>
            重置默认
          </Button>
          <Button onClick={onClose}>
            取消
          </Button>
          <Button type="primary" onClick={handleSave}>
            保存
          </Button>
        </Space>
      }
    >
      <Tabs items={tabItems} />
    </Modal>
  );
});
