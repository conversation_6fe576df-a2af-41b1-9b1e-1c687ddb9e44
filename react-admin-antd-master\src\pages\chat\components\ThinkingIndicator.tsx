import React, { useState, useEffect } from "react";
import { Avatar } from "antd";
import { RobotOutlined } from "@ant-design/icons";

interface ThinkingIndicatorProps {
  fontSize: number;
}

export const ThinkingIndicator: React.FC<ThinkingIndicatorProps> = ({
  fontSize,
}) => {
  const [currentText, setCurrentText] = useState("");
  const [dotCount, setDotCount] = useState(0);

  const thinkingTexts = [
    "正在思考",
    "分析问题中",
    "整理思路",
    "准备回复",
    "生成回答",
  ];

  useEffect(() => {
    const textInterval = setInterval(() => {
      const randomText =
        thinkingTexts[Math.floor(Math.random() * thinkingTexts.length)];
      setCurrentText(randomText);
    }, 2000);

    const dotInterval = setInterval(() => {
      setDotCount((prev) => (prev + 1) % 4);
    }, 500);

    return () => {
      clearInterval(textInterval);
      clearInterval(dotInterval);
    };
  }, []);

  return (
    <div
      style={{
        display: "flex",
        gap: "8px",
        padding: "8px 16px",
        alignItems: "flex-end",
      }}
    >
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-start",
          maxWidth: "100%", // 容器占满可用空间
          width: "100%",
          flex: 1,
        }}
      >
        <div
          style={{
            maxWidth: "95%", // 与气泡框宽度一致
            minWidth: "200px", // 与气泡框最小宽度一致

            color: "#333",
            fontSize: fontSize,
            lineHeight: 1.4,
            display: "flex",
            alignItems: "center",
            gap: "8px",
            width: "fit-content", // 内容自适应宽度
          }}
        >
          <div
            style={{
              display: "flex",
              gap: "4px",
            }}
          >
            <span
              style={{
                width: "6px",
                height: "6px",
                borderRadius: "50%",
                backgroundColor: "#52c41a",
                animation: "thinking 1.4s infinite ease-in-out",
                animationDelay: "-0.32s",
              }}
            ></span>
            <span
              style={{
                width: "6px",
                height: "6px",
                borderRadius: "50%",
                backgroundColor: "#52c41a",
                animation: "thinking 1.4s infinite ease-in-out",
                animationDelay: "-0.16s",
              }}
            ></span>
            <span
              style={{
                width: "6px",
                height: "6px",
                borderRadius: "50%",
                backgroundColor: "#52c41a",
                animation: "thinking 1.4s infinite ease-in-out",
              }}
            ></span>
          </div>
          <div
            style={{
              fontSize: fontSize * 0.75,
              color: "#999",
              marginBottom: "4px",
              padding: "0 4px",
            }}
          >
            {currentText}
            {"·".repeat(dotCount)}
          </div>
        </div>
      </div>

      <style>{`
        @keyframes thinking {
          0%, 80%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
          }
          40% {
            transform: scale(1.2);
            opacity: 1;
          }
        }
      `}</style>
    </div>
  );
};
