// API 基础地址
export const OPENAI_BASE_URL = "https://api.openai.com";
export const ANTHROPIC_BASE_URL = "https://api.anthropic.com";
export const GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/";
export const BAIDU_BASE_URL = "https://aip.baidubce.com";
export const BYTEDANCE_BASE_URL = "https://ark.cn-beijing.volces.com";
export const ALIBABA_BASE_URL = "https://dashscope.aliyuncs.com/api/";
export const TENCENT_BASE_URL = "https://hunyuan.tencentcloudapi.com";
export const MOONSHOT_BASE_URL = "https://api.moonshot.ai";
export const IFLYTEK_BASE_URL = "https://spark-api-open.xf-yun.com";
export const DEEPSEEK_BASE_URL = "https://api.deepseek.com";
export const XAI_BASE_URL = "https://api.x.ai";
export const CHATGLM_BASE_URL = "https://open.bigmodel.cn";

// 存储键名
export enum StoreKey {
  Chat = "chat-next-web-store",
  Access = "access-control",
  Config = "app-config",
  Mask = "mask-store",
  Prompt = "prompt-store",
  Update = "chat-update",
  Sync = "sync",
}

export enum FileName {
  Masks = "masks.json",
  Prompts = "prompts.json",
}

// 默认配置
export const DEFAULT_SIDEBAR_WIDTH = 300;
export const MAX_SIDEBAR_WIDTH = 500;
export const MIN_SIDEBAR_WIDTH = 230;

export const CHAT_PAGE_SIZE = 15;
export const MAX_RENDER_MSG_COUNT = 45;

export const REQUEST_TIMEOUT_MS = 60000;

export const EXPORT_MESSAGE_CLASS_NAME = "export-markdown";

export const DEFAULT_INPUT_TEMPLATE = `{{input}}`;
export const DEFAULT_SYSTEM_TEMPLATE = `You are ChatGPT, a large language model trained by OpenAI.
Knowledge cutoff: {{cutoff}}
Current model: {{model}}
Current time: {{time}}
Latex inline: $x^2$ 
Latex block: $$e=mc^2$$`;

export const SUMMARIZE_MODEL = "gpt-4o-mini";
export const GEMINI_SUMMARIZE_MODEL = "gemini-pro";
export const DEEPSEEK_SUMMARIZE_MODEL = "deepseek-chat";

export const KnowledgeCutOffDate: Record<string, string> = {
  default: "2023-10",
  "gpt-4-turbo": "2024-04",
  "gpt-4-turbo-2024-04-09": "2024-04",
  "gpt-4o": "2024-10",
  "gpt-4o-2024-05-13": "2024-10",
  "gpt-4o-2024-08-06": "2024-10",
  "gpt-4o-mini": "2024-10",
  "gpt-4o-mini-2024-07-18": "2024-10",
  "chatgpt-4o-latest": "2024-10",
};

export const DEFAULT_TTS_ENGINE = "OpenAI-TTS";
export const DEFAULT_TTS_ENGINES = ["OpenAI-TTS", "Edge-TTS"];
export const DEFAULT_TTS_MODEL = "tts-1";
export const DEFAULT_TTS_VOICE = "alloy";
export const DEFAULT_TTS_MODELS = ["tts-1", "tts-1-hd"];
export const DEFAULT_TTS_VOICES = [
  "alloy",
  "echo", 
  "fable",
  "onyx",
  "nova",
  "shimmer",
];

export const UNFINISHED_INPUT = (id: string) => "unfinished-input-" + id;

export const BUILTIN_MASK_ID = 100000;

export const DEFAULT_MODELS = [
  {
    name: "gpt-4o",
    available: true,
    provider: {
      id: "openai",
      providerName: "OpenAI",
      providerType: "openai",
    },
  },
  {
    name: "gpt-4o-mini", 
    available: true,
    provider: {
      id: "openai",
      providerName: "OpenAI", 
      providerType: "openai",
    },
  },
  {
    name: "gpt-3.5-turbo",
    available: true,
    provider: {
      id: "openai",
      providerName: "OpenAI",
      providerType: "openai", 
    },
  },
];

export const DEFAULT_TOPIC = "新的聊天";

export const ModelProvider = {
  OpenAI: "OpenAI",
  Azure: "Azure", 
  Anthropic: "Anthropic",
  Google: "Google",
  Baidu: "Baidu",
  ByteDance: "ByteDance",
  Alibaba: "Alibaba",
  Tencent: "Tencent",
  Moonshot: "Moonshot",
  Iflytek: "Iflytek",
  XAI: "XAI",
  ChatGLM: "ChatGLM",
  DeepSeek: "DeepSeek",
} as const;

// 提交键配置
export enum SubmitKey {
  Enter = "Enter",
  CtrlEnter = "Ctrl + Enter",
  ShiftEnter = "Shift + Enter",
  AltEnter = "Alt + Enter",
  MetaEnter = "Meta + Enter",
}

// 主题配置
export enum Theme {
  Auto = "auto",
  Dark = "dark", 
  Light = "light",
}

// 默认配置值
export const DEFAULT_CONFIG = {
  lastUpdate: Date.now(),
  submitKey: SubmitKey.Enter,
  avatar: "1f603",
  fontSize: 14,
  theme: Theme.Auto,
  tightBorder: false,
  sendPreviewBubble: true,
  enableAutoGenerateTitle: true,
  sidebarWidth: DEFAULT_SIDEBAR_WIDTH,
  disablePromptHint: false,
  dontShowMaskSplashScreen: false,
  hideBuiltinMasks: false,
  customModels: "",
  defaultModel: "gpt-3.5-turbo",
  
  modelConfig: {
    model: "gpt-3.5-turbo",
    providerName: "OpenAI",
    temperature: 0.5,
    top_p: 1,
    max_tokens: 4000,
    presence_penalty: 0,
    frequency_penalty: 0,
    sendMemory: true,
    historyMessageCount: 4,
    compressMessageLengthThreshold: 1000,
    compressModel: "gpt-3.5-turbo",
    compressProviderName: "OpenAI",
    enableInjectSystemPrompts: true,
    template: DEFAULT_INPUT_TEMPLATE,
    size: "1024x1024",
    quality: "standard",
    style: "vivid",
  },
  
  ttsConfig: {
    enable: false,
    autoplay: false,
    engine: DEFAULT_TTS_ENGINE,
    model: DEFAULT_TTS_MODEL,
    voice: DEFAULT_TTS_VOICE,
    speed: 1,
  },
};

// 内置面具
export const BUILTIN_MASKS = [
  {
    id: BUILTIN_MASK_ID,
    createdAt: Date.now(),
    avatar: "1f916",
    name: "助手",
    hideContext: false,
    context: [],
    syncGlobalConfig: true,
    modelConfig: DEFAULT_CONFIG.modelConfig,
    lang: "cn",
    builtin: true,
  },
];
