import { makeAutoObservable, runInAction } from 'mobx';
import { nanoid } from 'nanoid';
import { 
  ChatSession, 
  ChatMessage, 
  ChatStat, 
  Mask,
  RequestMessage,
  MultimodalContent 
} from '../types';
import { 
  StoreKey, 
  DEFAULT_TOPIC,
  CHAT_PAGE_SIZE,
  MAX_RENDER_MSG_COUNT,
  BUILTIN_MASKS 
} from '../constants';
import { createMessage, getMessageTextContent, trimTopic } from '../utils/message';

export function createEmptySession(): ChatSession {
  return {
    id: nanoid(),
    topic: DEFAULT_TOPIC,
    memoryPrompt: "",
    messages: [],
    stat: {
      tokenCount: 0,
      wordCount: 0,
      charCount: 0,
    },
    lastUpdate: Date.now(),
    lastSummarizeIndex: 0,
    mask: { ...BUILTIN_MASKS[0], id: BUILTIN_MASKS[0].id.toString() }, // 使用默认面具
  };
}

// 创建演示会话，包含各种Markdown格式的消息
export function createDemoSession(): ChatSession {
  const demoMessages: ChatMessage[] = [
    createMessage({
      role: "user",
      content: "你好！能帮我介绍一下React Hooks吗？",
    }),
    createMessage({
      role: "assistant",
      content: "你好！我很乐意为您介绍React Hooks。\n\n## 什么是React Hooks？\n\nReact Hooks是React 16.8引入的新特性，它让你可以在**函数组件**中使用状态和其他React特性。\n\n### 主要优势：\n\n1. **简化组件逻辑** - 不需要类组件\n2. **更好的代码复用** - 自定义Hooks\n3. **更容易测试** - 纯函数组件\n\n### 常用的Hooks：\n\n- `useState` - 状态管理\n- `useEffect` - 副作用处理\n- `useContext` - 上下文访问\n- `useCallback` - 回调函数优化\n- `useMemo` - 计算结果缓存"
    }),
    createMessage({
      role: "user",
      content: "能给我一个useState的代码示例吗？",
    }),
    createMessage({
      role: "assistant",
      content: "当然可以！这里是一个`useState`的完整示例：\n\n```jsx\nimport React, { useState } from 'react';\nimport { Button, Input, Card, Space } from 'antd';\n\nconst Counter = () => {\n  // 声明状态变量\n  const [count, setCount] = useState(0);\n  const [name, setName] = useState('');\n  \n  // 处理函数\n  const increment = () => setCount(count + 1);\n  const decrement = () => setCount(count - 1);\n  const reset = () => setCount(0);\n  \n  return (\n    <Card title=\"计数器示例\">\n      <Space direction=\"vertical\" style={{ width: '100%' }}>\n        <div>当前计数: <strong>{count}</strong></div>\n        \n        <Space>\n          <Button onClick={increment} type=\"primary\">+1</Button>\n          <Button onClick={decrement}>-1</Button>\n          <Button onClick={reset} danger>重置</Button>\n        </Space>\n        \n        <Input \n          placeholder=\"输入您的姓名\"\n          value={name}\n          onChange={(e) => setName(e.target.value)}\n        />\n        \n        {name && <div>你好, {name}！</div>}\n      </Space>\n    </Card>\n  );\n};\n\nexport default Counter;\n```\n\n### 关键点说明：\n\n> **状态初始化**：`useState(0)`设置初始值为0\n> \n> **状态更新**：`setCount(newValue)`更新状态\n> \n> **多个状态**：可以使用多个`useState`管理不同的状态\n\n这个例子展示了如何在函数组件中管理状态！"
    }),
    createMessage({
      role: "user",
      content: "useEffect有什么用？能对比一下生命周期方法吗？",
    }),
    createMessage({
      role: "assistant",
      content: "excellent question！`useEffect`是处理副作用的Hook，可以替代类组件的生命周期方法。\n\n## useEffect vs 生命周期方法\n\n| 类组件生命周期 | useEffect等价写法 | 用途 |\n|---------------|------------------|------|\n| `componentDidMount` | `useEffect(() => {}, [])` | 组件挂载后执行 |\n| `componentDidUpdate` | `useEffect(() => {})` | 每次更新后执行 |\n| `componentWillUnmount` | `useEffect(() => { return () => {} }, [])` | 组件卸载前清理 |\n\n### 基本用法示例：\n\n```javascript\nimport { useState, useEffect } from 'react';\n\nconst UserProfile = ({ userId }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  \n  // 相当于 componentDidMount + componentDidUpdate\n  useEffect(() => {\n    const fetchUser = async () => {\n      setLoading(true);\n      try {\n        const response = await fetch(`/api/users/${userId}`);\n        const userData = await response.json();\n        setUser(userData);\n      } catch (error) {\n        console.error('获取用户失败:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    \n    fetchUser();\n  }, [userId]); // 依赖数组：只在userId变化时重新执行\n  \n  // 清理副作用（相当于componentWillUnmount）\n  useEffect(() => {\n    const timer = setInterval(() => {\n      console.log('定时器执行');\n    }, 1000);\n    \n    // 返回清理函数\n    return () => {\n      clearInterval(timer);\n      console.log('清理定时器');\n    };\n  }, []);\n  \n  if (loading) return <div>加载中...</div>;\n  \n  return (\n    <div>\n      <h2>{user?.name}</h2>\n      <p>{user?.email}</p>\n    </div>\n  );\n};\n```\n\n### 🔑 核心概念：\n\n**依赖数组的作用：**\n- `[]` - 只在挂载时执行一次\n- `[value]` - 在value变化时执行\n- 无依赖数组 - 每次渲染都执行\n\n**清理函数：**\n- 用于清理订阅、定时器等\n- 在组件卸载或依赖变化前执行\n\n这样就能用函数组件实现所有生命周期的功能了！"
    }),
    createMessage({
      role: "user",
      content: "能总结一下React性能优化的方法吗？",
    }),
    createMessage({
      role: "assistant",
      content: "# React性能优化完整指南\n\n## 🚀 核心优化策略\n\n### 1. 组件层面优化\n\n#### React.memo\n防止不必要的重新渲染：\n\n```jsx\nconst ExpensiveComponent = React.memo(({ data, onClick }) => {\n  console.log('组件重新渲染');\n  return (\n    <div onClick={onClick}>\n      {data.map(item => <div key={item.id}>{item.name}</div>)}\n    </div>\n  );\n});\n\n// 自定义比较函数\nconst MyComponent = React.memo(({ user }) => {\n  return <div>{user.name}</div>;\n}, (prevProps, nextProps) => {\n  return prevProps.user.id === nextProps.user.id;\n});\n```\n\n#### useMemo 和 useCallback\n\n```jsx\nconst OptimizedComponent = ({ items, filter }) => {\n  // 缓存计算结果\n  const filteredItems = useMemo(() => {\n    return items.filter(item => item.category === filter);\n  }, [items, filter]);\n  \n  // 缓存回调函数\n  const handleClick = useCallback((id) => {\n    console.log('点击了:', id);\n  }, []);\n  \n  return (\n    <div>\n      {filteredItems.map(item => (\n        <Item \n          key={item.id} \n          data={item} \n          onClick={handleClick}\n        />\n      ))}\n    </div>\n  );\n};\n```\n\n### 2. 列表渲染优化\n\n#### 虚拟滚动\n对于大量数据的列表：\n\n```jsx\nimport { FixedSizeList as List } from 'react-window';\n\nconst VirtualizedList = ({ items }) => {\n  const Row = ({ index, style }) => (\n    <div style={style}>\n      {items[index].name}\n    </div>\n  );\n  \n  return (\n    <List\n      height={600}\n      itemCount={items.length}\n      itemSize={50}\n    >\n      {Row}\n    </List>\n  );\n};\n```\n\n### 3. 状态管理优化\n\n#### 状态分割\n避免单一大状态对象：\n\n```jsx\n// ❌ 不好的做法\nconst [state, setState] = useState({\n  user: {},\n  posts: [],\n  comments: [],\n  ui: {}\n});\n\n// ✅ 更好的做法\nconst [user, setUser] = useState({});\nconst [posts, setPosts] = useState([]);\nconst [comments, setComments] = useState([]);\nconst [ui, setUi] = useState({});\n```\n\n### 4. 代码分割\n\n#### 懒加载组件\n\n```jsx\nimport { lazy, Suspense } from 'react';\n\nconst LazyComponent = lazy(() => import('./HeavyComponent'));\n\nconst App = () => (\n  <Suspense fallback={<div>加载中...</div>}>\n    <LazyComponent />\n  </Suspense>\n);\n```\n\n## 📊 性能监控\n\n### React DevTools Profiler\n\n1. 安装React DevTools浏览器扩展\n2. 使用Profiler标签页分析性能\n3. 查看组件渲染时间和原因\n\n### 性能指标\n\n| 指标 | 说明 | 目标值 |\n|------|------|--------|\n| FCP | 首次内容绘制 | < 1.8s |\n| LCP | 最大内容绘制 | < 2.5s |\n| FID | 首次输入延迟 | < 100ms |\n| CLS | 累积布局偏移 | < 0.1 |\n\n## ⚡ 实用技巧\n\n### 避免内联对象和函数\n\n```jsx\n// ❌ 每次渲染都创建新对象\n<Component style={{marginTop: 10}} onClick={() => doSomething()} />\n\n// ✅ 提取到组件外部或使用useCallback\nconst styles = {marginTop: 10};\nconst handleClick = useCallback(() => doSomething(), []);\n<Component style={styles} onClick={handleClick} />\n```\n\n### 合理使用key\n\n```jsx\n// ❌ 使用数组索引\n{items.map((item, index) => <Item key={index} data={item} />)}\n\n// ✅ 使用稳定的唯一标识\n{items.map(item => <Item key={item.id} data={item} />)}\n```\n\n> **💡 性能优化原则**\n> \n> 1. **测量优先** - 先测量再优化\n> 2. **避免过早优化** - 关注真正的性能瓶颈\n> 3. **用户体验优先** - 优化用户感知的性能\n\n希望这个指南对您的React性能优化有帮助！"
    })
  ];

  return {
    id: nanoid(),
    topic: "React开发指南",
    memoryPrompt: "",
    messages: demoMessages,
    stat: {
      tokenCount: 2500,
      wordCount: 1200,
      charCount: 8000,
    },
    lastUpdate: Date.now(),
    lastSummarizeIndex: 0,
    mask: { ...BUILTIN_MASKS[0], id: BUILTIN_MASKS[0].id.toString() },
  };
}

export class ChatStore {
  sessions: ChatSession[] = [createEmptySession()];
  currentSessionIndex: number = 0;
  lastInput: string = "";
  
  constructor() {
    makeAutoObservable(this);
    this.loadFromLocalStorage();
  }

  get currentSession() {
    let index = this.currentSessionIndex;
    if (index < 0 || index >= this.sessions.length) {
      index = Math.min(this.sessions.length - 1, Math.max(0, index));
      this.currentSessionIndex = index;
    }

    const session = this.sessions[index];
    return session;
  }

  selectSession(index: number) {
    this.currentSessionIndex = index;
    this.saveToLocalStorage();
  }

  moveSession(from: number, to: number) {
    const { sessions } = this;
    const fromSession = sessions[from];
    sessions.splice(from, 1);
    sessions.splice(to, 0, fromSession);
    
    // 更新当前会话索引
    if (this.currentSessionIndex === from) {
      this.currentSessionIndex = to;
    } else if (this.currentSessionIndex > from && this.currentSessionIndex <= to) {
      this.currentSessionIndex -= 1;
    } else if (this.currentSessionIndex < from && this.currentSessionIndex >= to) {
      this.currentSessionIndex += 1;
    }
    
    this.saveToLocalStorage();
  }

  newSession(mask?: Mask) {
    const session = createEmptySession();

    if (mask) {
      session.mask = { ...mask };
      session.topic = mask.name;
    }

    // 将新会话插入到数组开头，而不是末尾
    this.sessions.unshift(session);
    this.currentSessionIndex = 0; // 新会话总是在索引0
    this.saveToLocalStorage();

    return session;
  }

  // 创建演示会话
  createDemoSession() {
    const demoSession = createDemoSession();
    // 将演示会话插入到数组开头
    this.sessions.unshift(demoSession);
    this.currentSessionIndex = 0; // 新会话总是在索引0
    this.saveToLocalStorage();
    return demoSession;
  }

  nextSession(delta: number) {
    const n = this.sessions.length;
    const limit = (x: number) => (x + n) % n;
    const i = limit(this.currentSessionIndex + delta);
    this.selectSession(i);
  }

  deleteSession(index: number) {
    if (this.sessions.length === 1) {
      // 如果只有一个会话，重置它而不是删除
      this.sessions[0] = createEmptySession();
      this.currentSessionIndex = 0;
    } else {
      // 记录删除前的当前会话
      const wasCurrentSession = this.currentSessionIndex === index;

      // 删除会话
      this.sessions.splice(index, 1);

      // 调整当前会话索引
      if (wasCurrentSession) {
        // 如果删除的是当前会话，选择相邻的会话
        if (index >= this.sessions.length) {
          // 如果删除的是最后一个，选择新的最后一个
          this.currentSessionIndex = this.sessions.length - 1;
        } else {
          // 否则保持当前索引（会自动指向下一个会话）
          this.currentSessionIndex = index;
        }
      } else if (this.currentSessionIndex > index) {
        // 如果删除的会话在当前会话之前，索引需要减1
        this.currentSessionIndex = this.currentSessionIndex - 1;
      }
      // 如果删除的会话在当前会话之后，索引不变
    }

    // 确保索引在有效范围内
    this.currentSessionIndex = Math.max(0, Math.min(this.currentSessionIndex, this.sessions.length - 1));

    this.saveToLocalStorage();
  }

  clearAllSessions() {
    this.sessions = [createEmptySession()];
    this.currentSessionIndex = 0;
    this.saveToLocalStorage();
  }

  onNewMessage(message: ChatMessage) {
    const session = this.currentSession;
    session.messages.push(message);
    session.lastUpdate = Date.now();
    this.updateCurrentSessionTopic();
    this.updateStat(message);
    this.saveToLocalStorage();
  }

  onUserInput(content: string, attachImages?: string[]) {
    const session = this.currentSession;
    
    let messageContent: string | MultimodalContent[];
    
    if (attachImages && attachImages.length > 0) {
      messageContent = [
        { type: "text", text: content },
        ...attachImages.map(url => ({
          type: "image_url" as const,
          image_url: { url }
        }))
      ];
    } else {
      messageContent = content;
    }

    const userMessage = createMessage({
      role: "user",
      content: messageContent,
    });

    this.onNewMessage(userMessage);
  }

  onBotMessage(content: string, streaming = false) {
    const session = this.currentSession;
    const botMessage = createMessage({
      role: "assistant",
      content,
      streaming,
    });

    this.onNewMessage(botMessage);
    return botMessage;
  }

  updateCurrentSessionTopic() {
    const session = this.currentSession;
    
    if (session.topic === DEFAULT_TOPIC && session.messages.length >= 2) {
      const firstUserMessage = session.messages.find(m => m.role === "user");
      if (firstUserMessage) {
        const content = getMessageTextContent(firstUserMessage);
        const topic = trimTopic(content.slice(0, 30));
        session.topic = topic || DEFAULT_TOPIC;
      }
    }
  }

  updateStat(message: ChatMessage) {
    const session = this.currentSession;
    const content = getMessageTextContent(message);
    
    session.stat.charCount += content.length;
    session.stat.wordCount += content.split(/\s+/).length;
    // 简单的token估算，实际应该使用tokenizer
    session.stat.tokenCount += Math.ceil(content.length / 4);
  }

  updateMessage(
    sessionIndex: number,
    messageIndex: number,
    updater: (message?: ChatMessage) => void,
  ) {
    const sessions = this.sessions;
    const session = sessions.at(sessionIndex);
    const messages = session?.messages;
    updater(messages?.at(messageIndex));
    this.saveToLocalStorage();
  }

  resetSession() {
    this.sessions[this.currentSessionIndex] = createEmptySession();
    this.saveToLocalStorage();
  }

  getMessagesWithMemory() {
    const session = this.currentSession;
    const config = session.mask.modelConfig;
    const clearContextIndex = session.clearContextIndex ?? 0;
    const messages = session.messages.slice();
    const totalMessageCount = session.messages.length;

    // 如果启用了记忆功能，添加记忆提示
    if (config.sendMemory && session.memoryPrompt && session.memoryPrompt.length > 0) {
      const memoryPrompt = createMessage({
        role: "system",
        content: session.memoryPrompt,
      });
      messages.unshift(memoryPrompt);
    }

    // 添加系统提示
    if (session.mask.context.length > 0) {
      messages.unshift(...session.mask.context);
    }

    // 限制历史消息数量
    const historyMsgCount = Math.min(
      config.historyMessageCount,
      totalMessageCount - clearContextIndex
    );
    
    const recentMessages = messages.slice(-historyMsgCount);
    
    return recentMessages;
  }

  saveToLocalStorage() {
    try {
      localStorage.setItem(StoreKey.Chat, JSON.stringify({
        sessions: this.sessions,
        currentSessionIndex: this.currentSessionIndex,
        lastInput: this.lastInput,
      }));
    } catch (error) {
      console.error('Failed to save chat store to localStorage:', error);
    }
  }

  loadFromLocalStorage() {
    try {
      const stored = localStorage.getItem(StoreKey.Chat);
      if (stored) {
        const data = JSON.parse(stored);
        this.sessions = data.sessions || [createEmptySession()];
        this.currentSessionIndex = data.currentSessionIndex || 0;
        this.lastInput = data.lastInput || "";
        
        // 确保当前会话索引有效
        if (this.currentSessionIndex >= this.sessions.length) {
          this.currentSessionIndex = 0;
        }
      }
    } catch (error) {
      console.error('Failed to load chat store from localStorage:', error);
      this.sessions = [createEmptySession()];
      this.currentSessionIndex = 0;
    }
  }

  clearAllData() {
    this.sessions = [createEmptySession()];
    this.currentSessionIndex = 0;
    this.lastInput = "";
    localStorage.removeItem(StoreKey.Chat);
  }

  searchSessions(keyword: string) {
    const results: Array<{
      sessionIndex: number;
      session: ChatSession;
      messageIndex: number;
      message: ChatMessage;
    }> = [];

    this.sessions.forEach((session, sessionIndex) => {
      session.messages.forEach((message, messageIndex) => {
        const content = getMessageTextContent(message);
        if (content.toLowerCase().includes(keyword.toLowerCase())) {
          results.push({
            sessionIndex,
            session,
            messageIndex,
            message,
          });
        }
      });
    });

    return results;
  }
}
