import { makeAutoObservable } from 'mobx';
import { nanoid } from 'nanoid';
import { Prompt, PromptHint } from '../types';
import { StoreKey } from '../constants';

export class PromptStore {
  prompts: Record<string, Prompt> = {};
  
  constructor() {
    makeAutoObservable(this);
    this.loadFromLocalStorage();
  }

  get allPrompts() {
    return Object.values(this.prompts).sort(
      (a, b) => b.createdAt - a.createdAt,
    );
  }

  get(id: string) {
    return this.prompts[id];
  }

  add(prompt: Prompt) {
    const newPrompt = { ...prompt };
    if (!newPrompt.id) {
      newPrompt.id = nanoid();
    }
    newPrompt.createdAt = Date.now();
    
    this.prompts[newPrompt.id] = newPrompt;
    this.saveToLocalStorage();
    
    return newPrompt;
  }

  remove(id: string) {
    delete this.prompts[id];
    this.saveToLocalStorage();
  }

  update(id: string, updater: (prompt: Prompt) => void) {
    const prompt = this.prompts[id];
    if (!prompt) return;
    
    updater(prompt);
    this.saveToLocalStorage();
  }

  search(text: string): PromptHint[] {
    const searchText = text.toLowerCase();
    return this.allPrompts
      .filter(
        (p) =>
          p.title.toLowerCase().includes(searchText) ||
          p.content.toLowerCase().includes(searchText),
      )
      .map((p) => ({
        id: p.id,
        title: p.title,
        content: p.content,
      }));
  }

  create(template?: Partial<Prompt>): Prompt {
    const newPrompt: Prompt = {
      id: nanoid(),
      title: "新提示词",
      content: "",
      createdAt: Date.now(),
      isUser: true,
      ...template,
    };

    return newPrompt;
  }

  clone(id: string) {
    const prompt = this.get(id);
    if (!prompt) return null;
    
    const clonedPrompt: Prompt = {
      ...prompt,
      id: nanoid(),
      title: `${prompt.title} (副本)`,
      createdAt: Date.now(),
    };
    
    this.prompts[clonedPrompt.id] = clonedPrompt;
    this.saveToLocalStorage();
    
    return clonedPrompt;
  }

  import(prompts: Prompt[]) {
    let importCount = 0;
    
    prompts.forEach((prompt) => {
      if (prompt.id) {
        // 生成新的ID避免冲突
        const newPrompt = {
          ...prompt,
          id: nanoid(),
          createdAt: Date.now(),
          isUser: true,
        };
        
        this.prompts[newPrompt.id] = newPrompt;
        importCount++;
      }
    });
    
    if (importCount > 0) {
      this.saveToLocalStorage();
    }
    
    return importCount;
  }

  export() {
    return Object.values(this.prompts);
  }

  // 获取用户提示词
  getUserPrompts() {
    return this.allPrompts.filter(p => p.isUser);
  }

  // 获取系统提示词
  getSystemPrompts() {
    return this.allPrompts.filter(p => !p.isUser);
  }

  // 按分类获取提示词
  getPromptsByCategory(category: string) {
    return this.allPrompts.filter(p => 
      p.title.toLowerCase().includes(category.toLowerCase()) ||
      p.content.toLowerCase().includes(category.toLowerCase())
    );
  }

  // 获取最近使用的提示词
  getRecentPrompts(limit: number = 10) {
    return this.allPrompts.slice(0, limit);
  }

  // 获取热门提示词（按使用频率）
  getPopularPrompts(limit: number = 10) {
    // 这里可以根据使用统计来排序，目前按创建时间
    return this.allPrompts.slice(0, limit);
  }

  // 搜索提示词并返回匹配的提示
  searchPrompts(keyword: string, limit: number = 5): PromptHint[] {
    if (!keyword.trim()) return [];
    
    const searchText = keyword.toLowerCase();
    const matches = this.allPrompts
      .filter(p => 
        p.title.toLowerCase().includes(searchText) ||
        p.content.toLowerCase().includes(searchText)
      )
      .slice(0, limit)
      .map(p => ({
        id: p.id,
        title: p.title,
        content: p.content,
      }));
    
    return matches;
  }

  // 获取提示词统计信息
  getStats() {
    const allPrompts = this.allPrompts;
    const userPrompts = this.getUserPrompts();
    const systemPrompts = this.getSystemPrompts();
    
    return {
      total: allPrompts.length,
      user: userPrompts.length,
      system: systemPrompts.length,
    };
  }

  // 批量删除提示词
  batchRemove(ids: string[]) {
    ids.forEach(id => {
      delete this.prompts[id];
    });
    this.saveToLocalStorage();
  }

  // 清空所有用户提示词
  clearUserPrompts() {
    const userPromptIds = this.getUserPrompts().map(p => p.id);
    this.batchRemove(userPromptIds);
  }

  saveToLocalStorage() {
    try {
      localStorage.setItem(StoreKey.Prompt, JSON.stringify(this.prompts));
    } catch (error) {
      console.error('Failed to save prompts to localStorage:', error);
    }
  }

  loadFromLocalStorage() {
    try {
      const stored = localStorage.getItem(StoreKey.Prompt);
      if (stored) {
        const data = JSON.parse(stored);
        this.prompts = data || {};
      }
    } catch (error) {
      console.error('Failed to load prompts from localStorage:', error);
      this.prompts = {};
    }
  }

  clearAllData() {
    this.prompts = {};
    localStorage.removeItem(StoreKey.Prompt);
  }
}
