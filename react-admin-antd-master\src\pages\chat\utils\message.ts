import { ChatMessage, MultimodalContent } from '../types';

export function getMessageTextContent(message: ChatMessage): string {
  if (typeof message.content === "string") {
    return message.content;
  }
  
  if (Array.isArray(message.content)) {
    return message.content
      .filter((item) => item.type === "text")
      .map((item) => item.text)
      .join("");
  }
  
  return "";
}

export function getMessageImages(message: ChatMessage): string[] {
  if (typeof message.content === "string") {
    return [];
  }
  
  if (Array.isArray(message.content)) {
    return message.content
      .filter((item) => item.type === "image_url")
      .map((item) => item.image_url?.url)
      .filter(Boolean) as string[];
  }
  
  return [];
}

export function createMessage(override: Partial<ChatMessage>): ChatMessage {
  return {
    id: Date.now().toString(),
    date: new Date().toLocaleString(),
    role: "user",
    content: "",
    ...override,
  };
}

export function isVisionModel(model: string) {
  // 支持视觉的模型列表
  const visionModels = [
    "gpt-4-vision-preview",
    "gpt-4-turbo",
    "gpt-4o",
    "gpt-4o-mini",
    "claude-3-opus",
    "claude-3-sonnet", 
    "claude-3-haiku",
    "gemini-pro-vision",
    "gemini-1.5-pro",
    "gemini-1.5-flash",
  ];
  
  return visionModels.some(visionModel => 
    model.toLowerCase().includes(visionModel.toLowerCase())
  );
}

export function compressImage(file: File, maxWidth: number = 1024): Promise<string> {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();
    
    img.onload = () => {
      const { width, height } = img;
      const ratio = Math.min(maxWidth / width, maxWidth / height);
      
      canvas.width = width * ratio;
      canvas.height = height * ratio;
      
      ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
      
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result as string);
            reader.onerror = reject;
            reader.readAsDataURL(blob);
          } else {
            reject(new Error("Canvas to blob conversion failed"));
          }
        },
        "image/jpeg",
        0.8
      );
    };
    
    img.onerror = reject;
    img.src = URL.createObjectURL(file);
  });
}

export function downloadAs(text: string, filename: string) {
  const element = document.createElement("a");
  element.setAttribute(
    "href",
    "data:text/plain;charset=utf-8," + encodeURIComponent(text)
  );
  element.setAttribute("download", filename);
  element.style.display = "none";
  document.body.appendChild(element);
  element.click();
  document.body.removeChild(element);
}

export function readFromFile() {
  return new Promise<string>((res, rej) => {
    const fileInput = document.createElement("input");
    fileInput.type = "file";
    fileInput.accept = "application/json";
    
    fileInput.onchange = (event: any) => {
      const file = event.target.files[0];
      const fileReader = new FileReader();
      fileReader.onload = (e: any) => {
        res(e.target.result);
      };
      fileReader.onerror = rej;
      fileReader.readAsText(file);
    };
    
    fileInput.click();
  });
}

export function copyToClipboard(text: string) {
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text);
  } else {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    try {
      document.execCommand('copy');
    } catch (err) {
      console.error('Unable to copy to clipboard', err);
    }
    document.body.removeChild(textArea);
  }
}

export function selectOrCopy(el: HTMLElement, content: string) {
  const currentSelection = window.getSelection();
  
  if (currentSelection?.type === "Range") {
    return false;
  }
  
  copyToClipboard(content);
  return true;
}

export function autoGrowTextArea(dom: HTMLTextAreaElement) {
  const prevHeight = dom.style.height;
  dom.style.height = "auto";
  const newHeight = dom.scrollHeight + "px";
  dom.style.height = newHeight;
  
  if (prevHeight !== newHeight) {
    return true;
  }
  return false;
}

export function getCSSVar(varName: string) {
  return getComputedStyle(document.body).getPropertyValue(varName).trim();
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function isImageFile(file: File): boolean {
  return file.type.startsWith('image/');
}

export function getFileExtension(filename: string): string {
  return filename.slice((filename.lastIndexOf(".") - 1 >>> 0) + 2);
}

export function trimTopic(topic: string) {
  return topic.replace(/[，。！？；、]/g, "").trim();
}
